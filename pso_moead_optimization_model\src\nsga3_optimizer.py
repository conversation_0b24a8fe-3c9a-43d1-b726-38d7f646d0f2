#!/usr/bin/env python3
"""
NSGA-III多目标优化算法实现

基于参考点的非支配排序遗传算法第三版(Non-dominated Sorting Genetic Algorithm III)
用于化工车间温度序列的多目标优化。

优化目标：
1. 最小化label_1预测值
2. 最大化label_2预测值（转换为最小化）
3. 最大化温度序列稳定性（转换为最小化）
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Callable, Optional, Any
import logging
import yaml
import random
import copy
from datetime import datetime
from scipy.spatial.distance import cdist
from scipy.interpolate import interp1d
import math

try:
    from .business_data_analyzer import BusinessDataAnalyzer
except ImportError:
    from business_data_analyzer import BusinessDataAnalyzer

logger = logging.getLogger(__name__)


class Individual:
    """个体类"""

    def __init__(self, decision_variables: np.ndarray):
        """
        初始化个体

        Args:
            decision_variables: 决策变量（温度序列）
        """
        self.decision_variables = decision_variables.copy()
        self.objectives = None
        self.rank = None
        self.crowding_distance = 0.0
        self.niche_count = 0

        # 基于分类学习的适应度评估属性
        self.pareto_class = None  # 帕累托分类结果 (0=较差, 1=优秀)
        self.pareto_probability = None  # 优秀类别的概率

    def __lt__(self, other):
        """比较操作符，用于排序"""
        if self.rank != other.rank:
            return self.rank < other.rank

        # 如果有分类概率信息，优先使用
        if hasattr(self, 'pareto_probability') and hasattr(other, 'pareto_probability'):
            if self.pareto_probability is not None and other.pareto_probability is not None:
                return self.pareto_probability > other.pareto_probability

        return self.crowding_distance > other.crowding_distance


class NSGA3Optimizer:
    """NSGA-III优化器"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        """
        初始化NSGA-III优化器

        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path  # 保存配置文件路径
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)

        # 温度序列参数
        temp_config = self.config.get('temperature_sequence', {})
        self.sequence_length = temp_config.get('sequence_length', 1000)
        self.min_temperature = temp_config.get('min_temperature', 13.0)
        self.max_temperature = temp_config.get('max_temperature', 152.0)

        # NSGA-III参数（如果配置文件中没有，使用默认值）
        nsga3_config = self.config.get('nsga3', {})

        self.population_size = nsga3_config.get('population_size', 50)
        self.max_generations = nsga3_config.get('max_generations', 200)
        self.crossover_prob = nsga3_config.get('crossover_prob', 0.9)
        self.mutation_prob = nsga3_config.get('mutation_prob', 0.1)
        self.eta_c = nsga3_config.get('eta_c', 20)
        self.eta_m = nsga3_config.get('eta_m', 20)

        # 目标函数数量
        self.n_objectives = 3

        # 生成参考点
        self.reference_points = self._generate_reference_points()

        # 优化状态
        self.population = []
        self.objective_function = None
        self.generation_history = []
        self.hypervolume_history = []

        # 从MOEAD继承的约束处理组件
        self.business_analyzer = None
        self.constraint_handler = None
        self.real_sample_sequences = None
        self.excluded_samples = [8, 13, 19]  # 按用户要求排除的样本
        self.constraint_curve_length = None
        self.average_temperature_curve = None
        self.lower_bound_curve = None
        self.upper_bound_curve = None
        self.enable_constraints = False  # 暂时禁用约束机制

        # 基于分类学习的适应度评估器
        self.pareto_classifier = None
        self.pareto_scaler = None
        self.feature_extractor = None

        logger.info(f"NSGA-III优化器初始化完成")
        logger.info(f"  种群大小: {self.population_size}")
        logger.info(f"  最大代数: {self.max_generations}")
        logger.info(f"  参考点数量: {len(self.reference_points)}")

    def set_pareto_classifier(self, pareto_classifier, pareto_scaler, feature_extractor):
        """
        设置基于分类学习的帕累托适应度评估器

        Args:
            pareto_classifier: 训练好的帕累托分类器
            pareto_scaler: 特征标准化器
            feature_extractor: 特征提取器
        """
        self.pareto_classifier = pareto_classifier
        self.pareto_scaler = pareto_scaler
        self.feature_extractor = feature_extractor
        logger.info("基于分类学习的帕累托适应度评估器已设置")
    
    def _generate_reference_points(self) -> np.ndarray:
        """
        生成参考点（Das and Dennis方法）
        
        Returns:
            参考点数组
        """
        # 对于3个目标，使用两层参考点
        # 第一层：较粗的分割
        p1 = 4  # 第一层分割数
        # 第二层：较细的分割
        p2 = 0  # 简化起见，只使用第一层
        
        def generate_unit_reference_points(n_partitions, n_objectives):
            """生成单位参考点"""
            if n_objectives == 1:
                return np.array([[1.0]])
            
            ref_points = []
            
            def generate_recursive(remaining_sum, current_point, remaining_dims):
                if remaining_dims == 1:
                    current_point.append(remaining_sum)
                    ref_points.append(current_point[:])
                    current_point.pop()
                else:
                    for i in range(remaining_sum + 1):
                        current_point.append(i / n_partitions)
                        generate_recursive(remaining_sum - i, current_point, remaining_dims - 1)
                        current_point.pop()
            
            generate_recursive(n_partitions, [], n_objectives)
            return np.array(ref_points)
        
        reference_points = generate_unit_reference_points(p1, self.n_objectives)
        
        logger.info(f"生成了 {len(reference_points)} 个参考点")
        return reference_points
    
    def set_objective_function(self, objective_function: Callable):
        """
        设置目标函数
        
        Args:
            objective_function: 目标函数，输入温度序列，返回目标函数值列表
        """
        self.objective_function = objective_function
        logger.info("目标函数已设置")
    
    def _initialize_population(self) -> List[Individual]:
        """
        初始化种群 - 使用MOEAD的基于真实样本数据的初始化策略

        Returns:
            初始种群
        """
        logger.info("基于18个实际样本数据进行种群初始化...")
        self._initialize_components()

        # 确保已计算平均温度曲线和约束边界
        if self.average_temperature_curve is None:
            logger.error("未计算平均温度曲线，无法初始化种群")
            raise ValueError("未计算平均温度曲线")

        if self.enable_constraints and (self.lower_bound_curve is None or self.upper_bound_curve is None):
            logger.error("启用约束但未计算约束边界，无法初始化种群")
            raise ValueError("未计算约束边界")

        # 使用BusinessDataAnalyzer的已加载数据
        if not hasattr(self.business_analyzer, 'temperature_sequences') or not self.business_analyzer.temperature_sequences:
            logger.error("BusinessDataAnalyzer中未找到温度序列数据，无法初始化种群")
            raise ValueError("未能获取真实样本数据")

        # 准备样本数据用于初始化
        sample_sequences = list(self.business_analyzer.temperature_sequences.values())
        sample_ids = list(self.business_analyzer.temperature_sequences.keys())

        logger.info(f"可用样本数量: {len(sample_sequences)}")
        logger.info(f"样本ID: {sample_ids}")

        # 计算目标序列长度（18个样本的平均长度）
        sample_lengths = [len(seq) for seq in sample_sequences]
        target_length = int(np.mean(sample_lengths))
        logger.info(f"目标序列长度: {target_length} (基于{len(sample_lengths)}个样本的平均长度)")

        # 更新序列长度
        self.sequence_length = target_length

        population = []

        # 策略1: 直接使用真实样本初始化部分个体
        num_real_samples = min(len(sample_sequences), self.population_size // 2)
        logger.info(f"使用{num_real_samples}个真实样本直接初始化个体")

        for i in range(num_real_samples):
            sample_sequence = sample_sequences[i % len(sample_sequences)]
            sample_id = sample_ids[i % len(sample_ids)]

            # 标准化到目标长度
            normalized_sequence = self._normalize_sequence_to_target_length(sample_sequence, target_length)

            # 应用约束机制
            if self.enable_constraints:
                normalized_sequence = self._apply_constraints(normalized_sequence)
            else:
                normalized_sequence = np.clip(normalized_sequence, self.min_temperature, self.max_temperature)

            individual = Individual(normalized_sequence)
            population.append(individual)

            logger.debug(f"个体{i}使用样本{sample_id}初始化，长度: {len(normalized_sequence)}")

        # 策略2: 基于真实样本的扰动生成剩余个体
        remaining_individuals = self.population_size - num_real_samples
        logger.info(f"基于样本扰动生成{remaining_individuals}个个体")

        for i in range(remaining_individuals):
            # 随机选择一个真实样本作为基础
            base_sample = sample_sequences[np.random.randint(0, len(sample_sequences))]
            base_normalized = self._normalize_sequence_to_target_length(base_sample, target_length)

            # 添加高斯噪声扰动
            noise_std = np.std(base_normalized) * 0.1  # 10%的标准差作为噪声强度
            perturbed_sequence = base_normalized + np.random.normal(0, noise_std, target_length)

            # 应用约束机制
            if self.enable_constraints:
                perturbed_sequence = self._apply_constraints(perturbed_sequence)
            else:
                perturbed_sequence = np.clip(perturbed_sequence, self.min_temperature, self.max_temperature)

            individual = Individual(perturbed_sequence)
            population.append(individual)

        # 评估初始种群
        self._evaluate_population(population)

        logger.info(f"种群初始化完成，总个体数: {len(population)}")
        logger.info(f"初始化策略: {num_real_samples}个真实样本 + {remaining_individuals}个扰动样本")
        logger.info(f"序列长度: {target_length} (基于18个样本的平均长度)")

        if self.enable_constraints:
            logger.info("约束机制已启用，所有个体受到[μ_curve-θ, μ_curve+θ]约束")
        else:
            logger.info("约束机制已禁用，个体可自由优化")

        return population

    def _normalize_sequence_to_target_length(self, sequence: np.ndarray, target_length: int) -> np.ndarray:
        """
        将序列标准化到目标长度

        Args:
            sequence: 原始序列
            target_length: 目标长度

        Returns:
            标准化后的序列
        """
        if len(sequence) == target_length:
            return sequence.copy()
        elif len(sequence) > target_length:
            # 下采样
            indices = np.linspace(0, len(sequence) - 1, target_length, dtype=int)
            return sequence[indices]
        else:
            # 上采样（插值）
            from scipy.interpolate import interp1d
            x_old = np.linspace(0, 1, len(sequence))
            x_new = np.linspace(0, 1, target_length)
            f = interp1d(x_old, sequence, kind='linear')
            return f(x_new)

    def _apply_constraints(self, sequence: np.ndarray) -> np.ndarray:
        """
        应用约束机制

        Args:
            sequence: 温度序列

        Returns:
            应用约束后的序列
        """
        if (self.average_temperature_curve is None or
            self.lower_bound_curve is None or
            self.upper_bound_curve is None):
            # 如果约束曲线未计算，使用基本边界
            return np.clip(sequence, self.min_temperature, self.max_temperature)

        # 确保序列长度与约束曲线一致
        if len(sequence) != len(self.average_temperature_curve):
            sequence = self._normalize_sequence_to_target_length(sequence, len(self.average_temperature_curve))

        # 应用约束边界
        constrained_sequence = np.clip(sequence, self.lower_bound_curve, self.upper_bound_curve)

        # 应用绝对温度边界
        constrained_sequence = np.clip(constrained_sequence, self.min_temperature, self.max_temperature)

        return constrained_sequence

    def _initialize_components(self):
        """初始化业务数据分析器和约束处理组件（从MOEAD继承）"""
        try:
            # 初始化业务数据分析器
            self.business_analyzer = BusinessDataAnalyzer(config_path=self.config_path)

            # 加载真实样本数据（排除指定样本）
            self.business_analyzer.load_all_temperature_data(excluded_samples=self.excluded_samples)

            # 计算平均温度曲线
            self.average_temperature_curve = self.business_analyzer.calculate_average_temperature_curve()

            if self.average_temperature_curve is not None:
                # 设置约束曲线长度
                self.constraint_curve_length = len(self.average_temperature_curve)
                logger.info(f"平均温度曲线计算完成，长度: {self.constraint_curve_length}")

                # 计算约束边界
                if self.enable_constraints:
                    self.lower_bound_curve, self.upper_bound_curve = self.business_analyzer.calculate_constraint_intervals()
                    if self.lower_bound_curve is not None and self.upper_bound_curve is not None:
                        logger.info("约束边界计算完成")
                    else:
                        logger.warning("约束边界计算失败，禁用约束机制")
                        self.enable_constraints = False
            else:
                logger.error("平均温度曲线计算失败")
                raise ValueError("无法计算平均温度曲线")

            logger.info("业务数据分析器和约束处理组件初始化完成")

        except Exception as e:
            logger.error(f"组件初始化失败: {e}")
            logger.error(f"错误详情: {str(e)}")
            self.enable_constraints = False
            # 重新抛出异常，因为没有这些组件无法正常工作
            raise

    def _calculate_constraint_curves(self):
        """计算约束曲线（从MOEAD继承）"""
        try:
            # 获取有效样本序列（排除指定样本）
            valid_sequences = []
            for sample_id, sequence in self.business_analyzer.temperature_sequences.items():
                if sample_id not in self.excluded_samples:
                    valid_sequences.append(sequence)

            if not valid_sequences:
                logger.error("没有有效的样本序列用于计算约束曲线")
                self.enable_constraints = False
                return

            # 找到最短序列长度作为约束曲线长度
            self.constraint_curve_length = min(len(seq) for seq in valid_sequences)
            logger.info(f"约束曲线长度设定为: {self.constraint_curve_length}")

            # 标准化所有序列到相同长度
            normalized_sequences = []
            for sequence in valid_sequences:
                normalized_seq = self._normalize_sequence_to_target_length(sequence, self.constraint_curve_length)
                normalized_sequences.append(normalized_seq)

            normalized_sequences = np.array(normalized_sequences)

            # 计算平均曲线
            self.average_temperature_curve = np.mean(normalized_sequences, axis=0)

            # 计算约束边界（±θ）
            theta = self.config.get('constraints', {}).get('theta', 10.0)
            self.lower_bound_curve = self.average_temperature_curve - theta
            self.upper_bound_curve = self.average_temperature_curve + theta

            # 应用绝对温度边界
            self.lower_bound_curve = np.clip(self.lower_bound_curve, self.min_temperature, self.max_temperature)
            self.upper_bound_curve = np.clip(self.upper_bound_curve, self.min_temperature, self.max_temperature)

            logger.info(f"约束曲线计算完成，约束边界θ: {theta}")

        except Exception as e:
            logger.error(f"约束曲线计算失败: {e}")
            self.enable_constraints = False

    def _normalize_sequence_to_target_length(self, sequence: np.ndarray, target_length: int) -> np.ndarray:
        """将序列标准化到目标长度（从MOEAD继承）"""
        if len(sequence) == target_length:
            return sequence.copy()

        # 使用线性插值进行重采样
        x_old = np.linspace(0, 1, len(sequence))
        x_new = np.linspace(0, 1, target_length)
        normalized_sequence = np.interp(x_new, x_old, sequence)

        return normalized_sequence

    class PSOStyleConstraintHandler:
        """PSO风格的约束处理器（从MOEAD继承）"""
        def __init__(self, average_curve: np.ndarray, lower_bound: np.ndarray, upper_bound: np.ndarray,
                     curve_length: int, logger):
            self.average_curve = average_curve
            self.lower_bound = lower_bound
            self.upper_bound = upper_bound
            self.curve_length = curve_length
            self.logger = logger

        def apply_constraints(self, sequence: np.ndarray) -> np.ndarray:
            """对温度序列应用约束"""
            if len(sequence) != self.curve_length:
                # 如果长度不匹配，先调整到约束曲线长度
                if len(sequence) > self.curve_length:
                    # 下采样
                    indices = np.linspace(0, len(sequence) - 1, self.curve_length, dtype=int)
                    sequence = sequence[indices]
                else:
                    # 上采样
                    x_old = np.linspace(0, 1, len(sequence))
                    x_new = np.linspace(0, 1, self.curve_length)
                    sequence = np.interp(x_new, x_old, sequence)

            # 应用边界约束
            constrained_sequence = np.clip(sequence, self.lower_bound, self.upper_bound)

            # 统计约束应用情况
            violations = np.sum((sequence < self.lower_bound) | (sequence > self.upper_bound))
            if violations > 0:
                self.logger.debug(f"约束应用: {violations}/{len(sequence)} 个点被修正")

            return constrained_sequence
    
    def _evaluate_population(self, population: List[Individual]):
        """
        评估种群中所有个体的目标函数值
        
        Args:
            population: 种群
        """
        for individual in population:
            if individual.objectives is None:
                if self.objective_function is not None:
                    objectives = self.objective_function(individual.decision_variables)
                    individual.objectives = np.array(objectives)
                else:
                    # 默认目标函数值
                    individual.objectives = np.array([1.0, 1.0, 1.0])
    
    def _non_dominated_sorting(self, population: List[Individual]) -> List[List[Individual]]:
        """
        基于分类学习的非支配排序

        Args:
            population: 种群

        Returns:
            按前沿分层的种群
        """
        if self.pareto_classifier is not None:
            return self._classification_based_sorting(population)
        else:
            return self._traditional_non_dominated_sorting(population)

    def _classification_based_sorting(self, population: List[Individual]) -> List[List[Individual]]:
        """
        基于分类学习的排序：使用帕累托分类器直接划分支配解集和非支配解集

        Args:
            population: 种群

        Returns:
            按分类结果分层的种群
        """
        logger.debug("使用基于分类学习的非支配排序")

        # 使用帕累托分类器评估每个个体的质量
        excellent_individuals = []
        poor_individuals = []

        for individual in population:
            try:
                # 提取特征
                features = self.feature_extractor.extract_sequence_features(individual.decision_variables)
                features_scaled = self.pareto_scaler.transform(features.reshape(1, -1))

                # 预测类别和概率 - 增强错误处理
                prediction = self.pareto_classifier.predict(features_scaled)[0]
                probabilities = self.pareto_classifier.predict_proba(features_scaled)[0]

                # 设置个体的分类信息
                individual.pareto_class = prediction  # 0=较差, 1=优秀
            except AttributeError as e:
                if 'monotonic_cst' in str(e):
                    logger.error("检测到 scikit-learn 版本兼容性问题")
                    logger.error("请升级 scikit-learn 到 1.4.0+ 版本或重新训练模型")
                    raise RuntimeError("模型版本不兼容") from e
                else:
                    logger.error(f"分类预测过程中发生属性错误: {e}")
                    raise e
            except Exception as e:
                logger.error(f"分类预测过程中发生错误: {e}")
                logger.error(f"个体决策变量形状: {individual.decision_variables.shape}")
                logger.error(f"特征形状: {features.shape if 'features' in locals() else 'N/A'}")
                raise e
            individual.pareto_probability = probabilities[1]  # 优秀类别的概率
            individual.rank = 0 if prediction == 1 else 1  # 优秀个体rank=0，较差个体rank=1

            if prediction == 1:  # 优秀个体
                excellent_individuals.append(individual)
            else:  # 较差个体
                poor_individuals.append(individual)

        # 在同一前沿内按概率排序
        excellent_individuals.sort(key=lambda x: x.pareto_probability, reverse=True)
        poor_individuals.sort(key=lambda x: x.pareto_probability, reverse=True)

        fronts = []
        if excellent_individuals:
            fronts.append(excellent_individuals)
        if poor_individuals:
            fronts.append(poor_individuals)

        logger.debug(f"分类结果: 优秀个体={len(excellent_individuals)}, 较差个体={len(poor_individuals)}")

        return fronts

    def _traditional_non_dominated_sorting(self, population: List[Individual]) -> List[List[Individual]]:
        """
        传统的非支配排序（备用方法）

        Args:
            population: 种群

        Returns:
            按前沿分层的种群
        """
        logger.debug("使用传统的非支配排序")

        fronts = []
        domination_count = [0] * len(population)
        dominated_solutions = [[] for _ in range(len(population))]

        # 计算支配关系
        for i in range(len(population)):
            for j in range(len(population)):
                if i != j:
                    if self._dominates(population[i].objectives, population[j].objectives):
                        dominated_solutions[i].append(j)
                    elif self._dominates(population[j].objectives, population[i].objectives):
                        domination_count[i] += 1

        # 第一前沿
        current_front = []
        for i in range(len(population)):
            if domination_count[i] == 0:
                population[i].rank = 0
                current_front.append(population[i])

        fronts.append(current_front)

        # 后续前沿
        front_index = 0
        while len(fronts[front_index]) > 0:
            next_front = []
            for individual_idx in range(len(population)):
                if population[individual_idx] in fronts[front_index]:
                    for dominated_idx in dominated_solutions[individual_idx]:
                        domination_count[dominated_idx] -= 1
                        if domination_count[dominated_idx] == 0:
                            population[dominated_idx].rank = front_index + 1
                            next_front.append(population[dominated_idx])

            fronts.append(next_front)
            front_index += 1

        return fronts[:-1]  # 移除最后的空前沿
    
    def _dominates(self, obj1: np.ndarray, obj2: np.ndarray) -> bool:
        """
        判断obj1是否支配obj2（所有目标都是最小化）
        
        Args:
            obj1: 目标函数值1
            obj2: 目标函数值2
            
        Returns:
            是否支配
        """
        return np.all(obj1 <= obj2) and np.any(obj1 < obj2)
    
    def _reference_point_association(self, population: List[Individual]) -> Tuple[List[int], np.ndarray]:
        """
        参考点关联
        
        Args:
            population: 种群
            
        Returns:
            (个体关联的参考点索引列表, 参考点的niche计数)
        """
        # 归一化目标函数值
        objectives_matrix = np.array([ind.objectives for ind in population])
        
        # 理想点（每个目标的最小值）
        ideal_point = np.min(objectives_matrix, axis=0)
        
        # 平移到理想点
        translated_objectives = objectives_matrix - ideal_point
        
        # 计算每个个体到每个参考点的距离
        associations = []
        niche_count = np.zeros(len(self.reference_points))
        
        for i, individual in enumerate(population):
            translated_obj = translated_objectives[i]
            
            # 计算到所有参考点的垂直距离
            distances = []
            for ref_point in self.reference_points:
                if np.linalg.norm(ref_point) == 0:
                    distance = np.linalg.norm(translated_obj)
                else:
                    # 计算点到直线的距离
                    projection = np.dot(translated_obj, ref_point) / np.dot(ref_point, ref_point)
                    projected_point = projection * ref_point
                    distance = np.linalg.norm(translated_obj - projected_point)
                distances.append(distance)
            
            # 关联到最近的参考点
            closest_ref_idx = np.argmin(distances)
            associations.append(closest_ref_idx)
            niche_count[closest_ref_idx] += 1
        
        return associations, niche_count
    
    def _environmental_selection(self, population: List[Individual]) -> List[Individual]:
        """
        基于分类学习的环境选择

        Args:
            population: 当前种群

        Returns:
            选择后的种群
        """
        if self.pareto_classifier is not None:
            return self._classification_based_selection(population)
        else:
            return self._traditional_environmental_selection(population)

    def _classification_based_selection(self, population: List[Individual]) -> List[Individual]:
        """
        基于分类学习的环境选择：优先选择分类器认为优秀的个体

        Args:
            population: 当前种群

        Returns:
            选择后的种群
        """
        logger.debug("使用基于分类学习的环境选择")

        # 基于分类学习的非支配排序
        fronts = self._non_dominated_sorting(population)

        # 选择个体
        selected = []
        front_index = 0

        # 优先选择优秀个体（第一前沿）
        while front_index < len(fronts) and len(selected) < self.population_size:
            current_front = fronts[front_index]

            if len(selected) + len(current_front) <= self.population_size:
                # 整个前沿都可以加入
                selected.extend(current_front)
            else:
                # 需要从当前前沿中选择部分个体
                remaining_slots = self.population_size - len(selected)

                # 按分类概率排序选择
                current_front.sort(key=lambda x: x.pareto_probability if x.pareto_probability is not None else 0.0,
                                 reverse=True)
                selected.extend(current_front[:remaining_slots])
                break

            front_index += 1

        logger.debug(f"环境选择完成: 选择了{len(selected)}个个体")

        return selected[:self.population_size]

    def _traditional_environmental_selection(self, population: List[Individual]) -> List[Individual]:
        """
        传统的环境选择（NSGA-III的核心，备用方法）

        Args:
            population: 当前种群

        Returns:
            选择后的种群
        """
        logger.debug("使用传统的环境选择")

        # 非支配排序
        fronts = self._non_dominated_sorting(population)

        # 选择个体
        selected = []
        front_index = 0

        # 添加完整的前沿
        while len(selected) + len(fronts[front_index]) <= self.population_size:
            selected.extend(fronts[front_index])
            front_index += 1
            if front_index >= len(fronts):
                break

        # 如果还需要更多个体，从下一个前沿中选择
        if len(selected) < self.population_size and front_index < len(fronts):
            remaining_slots = self.population_size - len(selected)
            last_front = fronts[front_index]

            # 参考点关联
            associations, niche_count = self._reference_point_association(selected + last_front)

            # 从最后一个前沿中选择个体
            selected_from_last_front = self._niching_selection(
                last_front,
                associations[len(selected):],
                niche_count,
                remaining_slots
            )
            selected.extend(selected_from_last_front)

        return selected[:self.population_size]
    
    def _niching_selection(self, candidates: List[Individual], associations: List[int], 
                          niche_count: np.ndarray, n_select: int) -> List[Individual]:
        """
        基于niche的选择
        
        Args:
            candidates: 候选个体
            associations: 个体关联的参考点
            niche_count: 参考点的niche计数
            n_select: 需要选择的个体数量
            
        Returns:
            选择的个体
        """
        selected = []
        remaining_candidates = list(range(len(candidates)))
        
        for _ in range(n_select):
            if not remaining_candidates:
                break
            
            # 找到niche计数最小的参考点
            min_niche_refs = np.where(niche_count == np.min(niche_count))[0]
            
            # 从这些参考点中随机选择一个
            selected_ref = np.random.choice(min_niche_refs)
            
            # 找到关联到这个参考点的候选个体
            associated_candidates = [i for i in remaining_candidates 
                                   if associations[i] == selected_ref]
            
            if associated_candidates:
                # 随机选择一个个体
                selected_idx = np.random.choice(associated_candidates)
                selected.append(candidates[selected_idx])
                remaining_candidates.remove(selected_idx)
                niche_count[selected_ref] += 1
            else:
                # 如果没有关联的个体，随机选择一个
                selected_idx = np.random.choice(remaining_candidates)
                selected.append(candidates[selected_idx])
                remaining_candidates.remove(selected_idx)
        
        return selected
    
    def _crossover(self, parent1: Individual, parent2: Individual) -> Tuple[Individual, Individual]:
        """
        模拟二进制交叉（SBX）- 集成MOEAD的约束处理

        Args:
            parent1: 父代1
            parent2: 父代2

        Returns:
            两个子代
        """
        if np.random.random() > self.crossover_prob:
            return copy.deepcopy(parent1), copy.deepcopy(parent2)

        x1 = parent1.decision_variables.copy()
        x2 = parent2.decision_variables.copy()

        for i in range(len(x1)):
            if np.random.random() <= 0.5:
                if abs(x1[i] - x2[i]) > 1e-14:
                    if x1[i] > x2[i]:
                        y1, y2 = x2[i], x1[i]
                    else:
                        y1, y2 = x1[i], x2[i]

                    # 计算beta
                    rand = np.random.random()
                    if rand <= 0.5:
                        beta = (2 * rand) ** (1.0 / (self.eta_c + 1))
                    else:
                        beta = (1.0 / (2 * (1 - rand))) ** (1.0 / (self.eta_c + 1))

                    # 生成子代
                    c1 = 0.5 * ((y1 + y2) - beta * (y2 - y1))
                    c2 = 0.5 * ((y1 + y2) + beta * (y2 - y1))

                    # 边界处理
                    c1 = np.clip(c1, self.min_temperature, self.max_temperature)
                    c2 = np.clip(c2, self.min_temperature, self.max_temperature)

                    if x1[i] > x2[i]:
                        x1[i], x2[i] = c2, c1
                    else:
                        x1[i], x2[i] = c1, c2

        # 应用MOEAD的约束处理
        if self.enable_constraints and self.constraint_curve_length is not None:
            # 创建PSO风格的约束处理器
            pso_constraint_handler = self.PSOStyleConstraintHandler(
                self.average_temperature_curve,
                self.lower_bound_curve,
                self.upper_bound_curve,
                self.constraint_curve_length,
                logger
            )
            x1 = pso_constraint_handler.apply_constraints(x1)
            x2 = pso_constraint_handler.apply_constraints(x2)
        else:
            # 仅应用基本温度边界
            x1 = np.clip(x1, self.min_temperature, self.max_temperature)
            x2 = np.clip(x2, self.min_temperature, self.max_temperature)

        # 强制应用绝对温度上限（基于真实样本分析）
        x1 = np.clip(x1, 13.0, 150.0)
        x2 = np.clip(x2, 13.0, 150.0)

        return Individual(x1), Individual(x2)
    
    def _mutation(self, individual: Individual) -> Individual:
        """
        多项式变异 - 集成MOEAD的约束处理

        Args:
            individual: 个体

        Returns:
            变异后的个体
        """
        mutated = copy.deepcopy(individual)
        x = mutated.decision_variables

        for i in range(len(x)):
            if np.random.random() <= self.mutation_prob:
                delta1 = (x[i] - self.min_temperature) / (self.max_temperature - self.min_temperature)
                delta2 = (self.max_temperature - x[i]) / (self.max_temperature - self.min_temperature)

                rand = np.random.random()
                mut_pow = 1.0 / (self.eta_m + 1.0)

                if rand <= 0.5:
                    xy = 1.0 - delta1
                    val = 2.0 * rand + (1.0 - 2.0 * rand) * (xy ** (self.eta_m + 1.0))
                    deltaq = val ** mut_pow - 1.0
                else:
                    xy = 1.0 - delta2
                    val = 2.0 * (1.0 - rand) + 2.0 * (rand - 0.5) * (xy ** (self.eta_m + 1.0))
                    deltaq = 1.0 - val ** mut_pow

                x[i] = x[i] + deltaq * (self.max_temperature - self.min_temperature)
                x[i] = np.clip(x[i], self.min_temperature, self.max_temperature)

        # 应用MOEAD的约束处理
        if self.enable_constraints and self.constraint_curve_length is not None:
            # 创建PSO风格的约束处理器
            pso_constraint_handler = self.PSOStyleConstraintHandler(
                self.average_temperature_curve,
                self.lower_bound_curve,
                self.upper_bound_curve,
                self.constraint_curve_length,
                logger
            )
            x = pso_constraint_handler.apply_constraints(x)
        else:
            # 仅应用基本温度边界
            x = np.clip(x, self.min_temperature, self.max_temperature)

        # 强制应用绝对温度上限（基于真实样本分析）
        x = np.clip(x, 13.0, 150.0)

        mutated.decision_variables = x
        return mutated
    
    def optimize(self) -> Dict[str, Any]:
        """
        执行NSGA-III优化
        
        Returns:
            优化结果字典
        """
        logger.info("开始NSGA-III优化...")
        start_time = datetime.now()
        
        # 初始化种群
        self.population = self._initialize_population()
        
        # 优化主循环
        for generation in range(self.max_generations):
            generation_start = datetime.now()
            
            # 生成子代
            offspring = []
            while len(offspring) < self.population_size:
                # 选择父代
                parent1 = random.choice(self.population)
                parent2 = random.choice(self.population)
                
                # 交叉
                child1, child2 = self._crossover(parent1, parent2)
                
                # 变异
                child1 = self._mutation(child1)
                child2 = self._mutation(child2)
                
                offspring.extend([child1, child2])
            
            offspring = offspring[:self.population_size]
            
            # 评估子代
            self._evaluate_population(offspring)
            
            # 合并父代和子代
            combined_population = self.population + offspring
            
            # 环境选择
            self.population = self._environmental_selection(combined_population)
            
            # 记录历史
            generation_time = (datetime.now() - generation_start).total_seconds()
            
            # 计算统计信息
            objectives_matrix = np.array([ind.objectives for ind in self.population])
            mean_objectives = np.mean(objectives_matrix, axis=0)
            
            generation_info = {
                'generation': generation,
                'time': generation_time,
                'mean_objectives': mean_objectives.tolist(),
                'best_objectives': np.min(objectives_matrix, axis=0).tolist()
            }
            
            self.generation_history.append(generation_info)
            
            # 日志输出
            if generation % 10 == 0 or generation == self.max_generations - 1:
                logger.info(f"代数 {generation}: "
                          f"平均目标值={mean_objectives}, "
                          f"最优目标值={np.min(objectives_matrix, axis=0)}")
        
        # 优化完成
        total_time = (datetime.now() - start_time).total_seconds()
        logger.info(f"NSGA-III优化完成，总耗时: {total_time:.2f}秒")
        
        # 准备结果
        return self._prepare_results(total_time)
    
    def _prepare_results(self, total_time: float) -> Dict[str, Any]:
        """
        准备优化结果
        
        Args:
            total_time: 总优化时间
            
        Returns:
            结果字典
        """
        # 获取帕累托前沿
        fronts = self._non_dominated_sorting(self.population)
        pareto_front = fronts[0] if fronts else []
        
        # 提取目标函数值
        pareto_objectives = [ind.objectives.tolist() for ind in pareto_front]
        
        results = {
            'algorithm': 'NSGA-III',
            'total_time': total_time,
            'generations': len(self.generation_history),
            'population_size': self.population_size,
            'pareto_front_size': len(pareto_front),
            'pareto_front': pareto_objectives,
            'best_solutions': pareto_front,
            'generation_history': self.generation_history,
            'reference_points': self.reference_points.tolist(),
            'final_population_objectives': [ind.objectives.tolist() for ind in self.population]
        }
        
        return results


def main():
    """测试NSGA-III优化器"""
    def test_objective_function(x):
        """测试目标函数"""
        return [
            np.sum(x**2),  # 最小化
            np.sum((x-1)**2),  # 最小化
            np.sum((x-2)**2)   # 最小化
        ]
    
    optimizer = NSGA3Optimizer()
    optimizer.set_objective_function(test_objective_function)
    
    results = optimizer.optimize()
    
    print("NSGA-III优化结果:")
    print(f"  总时间: {results['total_time']:.2f}秒")
    print(f"  帕累托前沿大小: {results['pareto_front_size']}")
    print(f"  参考点数量: {len(results['reference_points'])}")


if __name__ == "__main__":
    main()
