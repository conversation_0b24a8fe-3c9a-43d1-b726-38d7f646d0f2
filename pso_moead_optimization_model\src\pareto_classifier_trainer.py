#!/usr/bin/env python3
"""
基于帕累托支配关系的温度序列分类器训练器

根据label_1、label_2和温度序列稳定性指标，使用帕累托支配关系将21个样本
分为两个类别：较优秀的温度序列和较差的温度序列。
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import logging
import yaml
import os
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import cross_val_score, train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, confusion_matrix
import joblib

try:
    from .data_processor import DataProcessor
    from .feature_extractor import FeatureExtractor
    from .sequence_stability_evaluator import SequenceStabilityEvaluator
except ImportError:
    from data_processor import DataProcessor
    from feature_extractor import FeatureExtractor
    from sequence_stability_evaluator import SequenceStabilityEvaluator

logger = logging.getLogger(__name__)


class ParetoClassifierTrainer:
    """基于帕累托支配关系的分类器训练器"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        """
        初始化帕累托分类器训练器
        
        Args:
            config_path: 配置文件路径
        """
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        self.data_processor = DataProcessor(config_path)
        self.feature_extractor = FeatureExtractor(config_path)
        self.stability_evaluator = SequenceStabilityEvaluator()
        
        # 存储数据
        self.temperature_sequences = {}
        self.quality_labels = {}
        self.stability_scores = {}
        self.pareto_classification = {}
        
        # 分类器
        self.sequence_classifier = None
        self.scaler = None
        
    def load_and_analyze_data(self) -> Dict[str, Any]:
        """
        加载并分析所有数据
        
        Returns:
            数据分析结果字典
        """
        logger.info("加载和分析数据...")
        
        # 加载温度序列和质量标签
        self.temperature_sequences = self.data_processor.load_temperature_sequences()
        self.quality_labels = self.data_processor.load_quality_labels()
        
        # 计算稳定性评分
        logger.info("计算温度序列稳定性评分...")
        for sample_id, sequence in self.temperature_sequences.items():
            stability_score = self.stability_evaluator.calculate_stability_score(sequence)
            self.stability_scores[sample_id] = stability_score
            logger.info(f"样本 {sample_id} 稳定性评分: {stability_score:.4f}")
        
        # 构建目标函数矩阵
        objectives_matrix = self._build_objectives_matrix()
        
        # 进行帕累托分析
        pareto_results = self._perform_pareto_analysis(objectives_matrix)
        
        return {
            'sample_count': len(self.temperature_sequences),
            'objectives_matrix': objectives_matrix,
            'pareto_classification': self.pareto_classification,
            'pareto_results': pareto_results
        }
    
    def _build_objectives_matrix(self) -> np.ndarray:
        """
        构建目标函数矩阵
        
        Returns:
            目标函数矩阵 [n_samples, 3]
            - 列0: label_1 (最小化，越小越好)
            - 列1: -label_2 (最大化转最小化，越小越好)
            - 列2: -稳定性 (最大化转最小化，越小越好)
        """
        sample_ids = sorted(self.temperature_sequences.keys())
        n_samples = len(sample_ids)
        objectives = np.zeros((n_samples, 3))
        
        for i, sample_id in enumerate(sample_ids):
            # 目标1: label_1 (最小化)
            if 'label_1' in self.quality_labels:
                label1_idx = sample_id - 1  # 转换为0-based索引
                objectives[i, 0] = self.quality_labels['label_1'][label1_idx]
            
            # 目标2: -label_2 (最大化转最小化)
            if 'label_2' in self.quality_labels:
                label2_idx = sample_id - 1
                objectives[i, 1] = -self.quality_labels['label_2'][label2_idx]
            
            # 目标3: -稳定性 (最大化转最小化)
            objectives[i, 2] = -self.stability_scores[sample_id]
        
        logger.info("目标函数矩阵构建完成:")
        logger.info(f"  形状: {objectives.shape}")
        logger.info(f"  目标1 (label_1) 范围: [{objectives[:, 0].min():.4f}, {objectives[:, 0].max():.4f}]")
        logger.info(f"  目标2 (-label_2) 范围: [{objectives[:, 1].min():.4f}, {objectives[:, 1].max():.4f}]")
        logger.info(f"  目标3 (-稳定性) 范围: [{objectives[:, 2].min():.4f}, {objectives[:, 2].max():.4f}]")
        
        return objectives
    
    def _perform_pareto_analysis(self, objectives_matrix: np.ndarray) -> Dict[str, Any]:
        """
        执行基于综合排序的样本分类分析

        Args:
            objectives_matrix: 目标函数矩阵

        Returns:
            分类分析结果
        """
        logger.info("执行基于综合排序的样本分类分析...")

        n_samples = objectives_matrix.shape[0]
        sample_ids = sorted(self.temperature_sequences.keys())

        # 计算每个样本的综合评分
        sample_scores = []

        for i, sample_id in enumerate(sample_ids):
            # 方法1: 计算支配其他样本的个数
            domination_score = self._count_dominated_solutions(objectives_matrix, i)

            # 方法2: 计算被支配的个数（越少越好）
            dominated_by_score = n_samples - self._count_dominating_solutions(objectives_matrix, i)

            # 方法3: 计算标准化后的目标函数值总和（越小越好）
            normalized_objectives = self._normalize_objectives(objectives_matrix[i], objectives_matrix)
            objective_sum_score = n_samples - np.sum(normalized_objectives)  # 转换为越大越好

            # 方法4: 计算到理想点的距离（越小越好）
            ideal_point = np.min(objectives_matrix, axis=0)
            distance_to_ideal = np.linalg.norm(objectives_matrix[i] - ideal_point)
            max_distance = np.max([np.linalg.norm(obj - ideal_point) for obj in objectives_matrix])
            distance_score = n_samples * (1 - distance_to_ideal / max_distance)  # 转换为越大越好

            # 综合评分（加权平均）
            comprehensive_score = (
                0.4 * domination_score +           # 支配能力
                0.3 * dominated_by_score +         # 抗支配能力
                0.2 * objective_sum_score +        # 目标函数优势
                0.1 * distance_score               # 理想点距离
            )

            sample_scores.append({
                'sample_id': sample_id,
                'index': i,
                'comprehensive_score': comprehensive_score,
                'domination_score': domination_score,
                'dominated_by_score': dominated_by_score,
                'objective_sum_score': objective_sum_score,
                'distance_score': distance_score
            })

        # 按综合评分排序
        sample_scores.sort(key=lambda x: x['comprehensive_score'], reverse=True)

        # 划分为两个等级：前50%为excellent，后50%为poor
        split_point = len(sample_scores) // 2

        excellent_samples = []
        poor_samples = []

        for i, score_info in enumerate(sample_scores):
            sample_id = score_info['sample_id']
            if i < split_point:
                self.pareto_classification[sample_id] = 'excellent'
                excellent_samples.append(sample_id)
            else:
                self.pareto_classification[sample_id] = 'poor'
                poor_samples.append(sample_id)

        logger.info("基于综合排序的分类结果:")
        logger.info(f"  优秀样本: {excellent_samples} (共{len(excellent_samples)}个)")
        logger.info(f"  较差样本: {poor_samples} (共{len(poor_samples)}个)")

        # 输出详细的评分信息
        logger.info("详细评分信息:")
        for score_info in sample_scores:
            logger.info(f"  样本{score_info['sample_id']}: 综合评分={score_info['comprehensive_score']:.3f}, "
                       f"支配能力={score_info['domination_score']}, "
                       f"抗支配能力={score_info['dominated_by_score']:.1f}")

        return {
            'sample_scores': sample_scores,
            'excellent_samples': excellent_samples,
            'poor_samples': poor_samples,
            'excellent_count': len(excellent_samples),
            'poor_count': len(poor_samples),
            'split_method': 'comprehensive_ranking'
        }
    
    def _find_pareto_front(self, objectives_matrix: np.ndarray) -> List[int]:
        """
        找到帕累托前沿
        
        Args:
            objectives_matrix: 目标函数矩阵
            
        Returns:
            帕累托前沿解的索引列表
        """
        n_samples = objectives_matrix.shape[0]
        pareto_front = []
        
        for i in range(n_samples):
            is_dominated = False
            for j in range(n_samples):
                if i != j and self._dominates(objectives_matrix[j], objectives_matrix[i]):
                    is_dominated = True
                    break
            
            if not is_dominated:
                pareto_front.append(i)
        
        return pareto_front
    
    def _dominates(self, solution1: np.ndarray, solution2: np.ndarray) -> bool:
        """
        判断solution1是否支配solution2（所有目标都是最小化）
        
        Args:
            solution1: 解1的目标函数值
            solution2: 解2的目标函数值
            
        Returns:
            是否支配
        """
        # 所有目标都不差于solution2，且至少一个目标更好
        all_not_worse = np.all(solution1 <= solution2)
        at_least_one_better = np.any(solution1 < solution2)
        
        return all_not_worse and at_least_one_better
    
    def _count_dominating_solutions(self, objectives_matrix: np.ndarray, solution_index: int) -> int:
        """
        计算支配指定解的解的数量

        Args:
            objectives_matrix: 目标函数矩阵
            solution_index: 解的索引

        Returns:
            支配该解的解的数量
        """
        count = 0
        target_solution = objectives_matrix[solution_index]

        for i in range(objectives_matrix.shape[0]):
            if i != solution_index and self._dominates(objectives_matrix[i], target_solution):
                count += 1

        return count

    def _count_dominated_solutions(self, objectives_matrix: np.ndarray, solution_index: int) -> int:
        """
        计算指定解支配的解的数量

        Args:
            objectives_matrix: 目标函数矩阵
            solution_index: 解的索引

        Returns:
            该解支配的解的数量
        """
        count = 0
        target_solution = objectives_matrix[solution_index]

        for i in range(objectives_matrix.shape[0]):
            if i != solution_index and self._dominates(target_solution, objectives_matrix[i]):
                count += 1

        return count

    def _normalize_objectives(self, objectives: np.ndarray, objectives_matrix: np.ndarray) -> np.ndarray:
        """
        标准化目标函数值

        Args:
            objectives: 单个解的目标函数值
            objectives_matrix: 所有解的目标函数矩阵

        Returns:
            标准化后的目标函数值
        """
        min_vals = np.min(objectives_matrix, axis=0)
        max_vals = np.max(objectives_matrix, axis=0)

        # 避免除零
        ranges = max_vals - min_vals
        ranges[ranges == 0] = 1.0

        normalized = (objectives - min_vals) / ranges
        return normalized
    
    def train_pareto_classifier(self) -> Dict[str, float]:
        """
        训练基于帕累托分类的温度序列分类器
        
        Returns:
            训练结果字典
        """
        logger.info("训练基于帕累托分类的温度序列分类器...")
        
        if not self.pareto_classification:
            raise ValueError("请先执行帕累托分析")
        
        # 准备训练数据
        X_list = []
        y_list = []
        
        for sample_id, sequence in self.temperature_sequences.items():
            if sample_id in self.pareto_classification:
                # 提取特征
                features = self.feature_extractor.extract_sequence_features(sequence)
                X_list.append(features)
                
                # 标签：1表示优秀，0表示较差
                label = 1 if self.pareto_classification[sample_id] == 'excellent' else 0
                y_list.append(label)
        
        if len(X_list) == 0:
            raise ValueError("没有有效的训练数据")
        
        # 转换为数组
        X = np.array(X_list)
        y = np.array(y_list)
        
        logger.info(f"训练数据形状: {X.shape}")
        logger.info(f"标签分布: 优秀={np.sum(y)}, 较差={len(y) - np.sum(y)}")
        
        # 标准化特征
        self.scaler = StandardScaler()
        X_scaled = self.scaler.fit_transform(X)
        
        # 训练分类器
        self.sequence_classifier = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            random_state=42,
            class_weight='balanced'  # 处理类别不平衡
        )
        
        self.sequence_classifier.fit(X_scaled, y)
        
        # 交叉验证评估
        cv_scores = cross_val_score(self.sequence_classifier, X_scaled, y, cv=5, scoring='accuracy')
        
        # 详细评估
        if len(np.unique(y)) > 1:  # 确保有两个类别
            X_train, X_test, y_train, y_test = train_test_split(
                X_scaled, y, test_size=0.3, random_state=42, stratify=y
            )
            
            self.sequence_classifier.fit(X_train, y_train)
            y_pred = self.sequence_classifier.predict(X_test)
            
            logger.info("分类报告:")
            logger.info(f"\n{classification_report(y_test, y_pred)}")
        
        logger.info(f"帕累托分类器训练完成，交叉验证准确率: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
        
        return {
            'cv_accuracy_mean': cv_scores.mean(),
            'cv_accuracy_std': cv_scores.std(),
            'feature_count': X.shape[1],
            'sample_count': X.shape[0],
            'excellent_count': np.sum(y),
            'poor_count': len(y) - np.sum(y)
        }
    
    def predict_sequence_quality(self, temperature_sequence: np.ndarray) -> Dict[str, float]:
        """
        预测温度序列的质量类别
        
        Args:
            temperature_sequence: 温度序列
            
        Returns:
            预测结果字典
        """
        if self.sequence_classifier is None or self.scaler is None:
            raise ValueError("分类器尚未训练")
        
        # 提取特征
        features = self.feature_extractor.extract_sequence_features(temperature_sequence)
        features_scaled = self.scaler.transform(features.reshape(1, -1))
        
        # 预测
        prediction = self.sequence_classifier.predict(features_scaled)[0]
        probabilities = self.sequence_classifier.predict_proba(features_scaled)[0]
        
        return {
            'prediction': int(prediction),
            'probability_excellent': float(probabilities[1]),
            'probability_poor': float(probabilities[0]),
            'confidence': float(np.max(probabilities)),
            'interpretation': "优秀序列" if prediction == 1 else "较差序列"
        }
    
    def save_classifier(self, output_dir: str = "models"):
        """
        保存训练好的分类器

        Args:
            output_dir: 输出目录
        """
        if self.sequence_classifier is None or self.scaler is None:
            raise ValueError("分类器尚未训练")

        os.makedirs(output_dir, exist_ok=True)

        # 保存分类器
        classifier_path = os.path.join(output_dir, "pareto_sequence_classifier.joblib")
        joblib.dump(self.sequence_classifier, classifier_path)

        # 保存标准化器
        scaler_path = os.path.join(output_dir, "pareto_sequence_scaler.joblib")
        joblib.dump(self.scaler, scaler_path)

        # 保存帕累托分类结果
        classification_path = os.path.join(output_dir, "pareto_classification.joblib")
        joblib.dump(self.pareto_classification, classification_path)

        # 保存特征提取器（关键！）
        feature_extractor_path = os.path.join(output_dir, "feature_extractor.joblib")
        joblib.dump(self.feature_extractor, feature_extractor_path)

        logger.info(f"帕累托分类器已保存到 {output_dir}")
        logger.info(f"  分类器: {classifier_path}")
        logger.info(f"  标准化器: {scaler_path}")
        logger.info(f"  分类结果: {classification_path}")
        logger.info(f"  特征提取器: {feature_extractor_path}")


def main():
    """测试帕累托分类器训练器"""
    trainer = ParetoClassifierTrainer()
    
    # 加载和分析数据
    analysis_results = trainer.load_and_analyze_data()
    print("数据分析结果:")
    print(f"  样本数量: {analysis_results['sample_count']}")
    print(f"  优秀样本: {analysis_results['pareto_results']['excellent_count']}")
    print(f"  较差样本: {analysis_results['pareto_results']['poor_count']}")
    
    # 训练分类器
    training_results = trainer.train_pareto_classifier()
    print("\n训练结果:")
    for key, value in training_results.items():
        print(f"  {key}: {value}")
    
    # 保存分类器
    trainer.save_classifier()


if __name__ == "__main__":
    main()
