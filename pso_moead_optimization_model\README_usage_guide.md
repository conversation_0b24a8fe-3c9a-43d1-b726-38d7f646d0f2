# 基于分类学习的NSGA-III优化系统使用指南

## 🚀 快速开始

### 方式一：完整流程（推荐新用户）

如果你是第一次使用系统，按以下顺序执行：

```bash
# 1. 训练标签分类器（label1和label2）
python train_classifier.py --train-classification-predictor --label1-bins 5 --label2-bins 5

# 2. 训练帕累托分类器
python train_pareto_classifier.py --save-analysis

# 3. 执行NSGA-III优化（不需要重新训练）
python run_classification_nsga3.py --save-plots
```

### 方式二：快速执行（自动训练）

如果想一次性完成所有步骤：

```bash
# 一键执行：自动训练帕累托分类器并优化
python run_classification_nsga3.py --save-plots --train-pareto-classifier
```

### 方式三：仅优化（已训练好分类器）

如果已经训练好所有分类器，直接执行优化：

```bash
# 直接优化（推荐）
python run_classification_nsga3.py --save-plots
```

## 📋 命令参数说明

### train_classifier.py 参数
```bash
python train_classifier.py [选项]

必要参数：
  --train-classification-predictor    启用分类预测器训练

可选参数：
  --config CONFIG                     配置文件路径 (默认: config/config.yaml)
  --data-dir DATA_DIR                 数据目录路径
  --output-dir OUTPUT_DIR             模型输出目录 (默认: models)
  --label1-bins LABEL1_BINS           Label1分箱数量 (默认: 5)
  --label2-bins LABEL2_BINS           Label2分箱数量 (默认: 5)
  --verbose                           详细输出模式
```

### train_pareto_classifier.py 参数
```bash
python train_pareto_classifier.py [选项]

可选参数：
  --config CONFIG                     配置文件路径 (默认: config/config.yaml)
  --data-dir DATA_DIR                 数据目录路径
  --output-dir OUTPUT_DIR             模型输出目录 (默认: models)
  --save-analysis                     保存帕累托分析结果
  --verbose                           详细输出模式
```

### run_classification_nsga3.py 参数
```bash
python run_classification_nsga3.py [选项]

可选参数：
  --config CONFIG                     配置文件路径 (默认: config/config.yaml)
  --models-dir MODELS_DIR             训练好的模型目录 (默认: models)
  --output-dir OUTPUT_DIR             结果输出目录 (默认: results)
  --generations GENERATIONS           优化代数（覆盖配置文件设置）
  --population-size POPULATION_SIZE   种群大小（覆盖配置文件设置）
  --train-pareto-classifier           重新训练帕累托分类器（如果已训练好则不需要此参数）
  --save-plots                        保存优化结果图表
  --verbose                           详细输出模式
```

## 🎯 三种使用场景

### 场景1：首次使用系统
```bash
# 完整训练流程
python train_classifier.py --train-classification-predictor --label1-bins 5 --label2-bins 5
python train_pareto_classifier.py --save-analysis
python run_classification_nsga3.py --save-plots
```

### 场景2：已有部分模型，需要重新训练帕累托分类器
```bash
# 重新训练帕累托分类器并优化
python run_classification_nsga3.py --save-plots --train-pareto-classifier
```

### 场景3：所有模型已训练好，仅需优化
```bash
# 直接优化
python run_classification_nsga3.py --save-plots
```

## 📁 模型文件说明

系统会在`models/`目录下生成以下文件：

### 必要文件
- `feature_extractor.joblib` - 特征提取器（必须）

### 标签分类器文件
- `label1_classifier.joblib` - Label1分类器（用于目标函数F1）
- `label2_classifier.joblib` - Label2分类器（用于目标函数F2）

### 帕累托分类器文件
- `pareto_sequence_classifier.joblib` - 帕累托分类器（用于目标函数F3）
- `pareto_sequence_scaler.joblib` - 帕累托特征标准化器

### 序列比较分类器文件
- `sequence_classifier.joblib` - 序列比较分类器（备用）

## ⚠️ 重要提示

### 关于 --train-pareto-classifier 参数

- **不使用此参数**：系统会直接加载已训练好的帕累托分类器
- **使用此参数**：系统会重新训练帕累托分类器（会覆盖现有模型）

### 推荐工作流程

1. **首次使用**：
   ```bash
   python train_classifier.py --train-classification-predictor
   python train_pareto_classifier.py --save-analysis
   python run_classification_nsga3.py --save-plots
   ```

2. **日常使用**：
   ```bash
   python run_classification_nsga3.py --save-plots
   ```

3. **需要重新训练时**：
   ```bash
   python run_classification_nsga3.py --save-plots --train-pareto-classifier
   ```

## 📊 结果文件

优化完成后，`results/`目录下会生成：

- `classification_nsga3_results_YYYYMMDD_HHMMSS.json` - 完整优化结果
- `classification_nsga3_pareto_front_YYYYMMDD_HHMMSS.csv` - Pareto前沿数据
- `classification_nsga3_best_solutions_YYYYMMDD_HHMMSS.json` - 最优解集
- `classification_nsga3_results_YYYYMMDD_HHMMSS.png` - 结果可视化图表（如果使用--save-plots）

## 🔧 自定义参数

### 调整优化参数
```bash
# 自定义代数和种群大小
python run_classification_nsga3.py --generations 300 --population-size 100 --save-plots
```

### 使用不同配置文件
```bash
# 使用自定义配置
python run_classification_nsga3.py --config my_config.yaml --save-plots
```

### 指定输出目录
```bash
# 指定结果输出目录
python run_classification_nsga3.py --output-dir my_results --save-plots
```

## 🐛 常见问题

### Q: 提示缺少模型文件怎么办？
A: 按照完整流程重新训练分类器：
```bash
python train_classifier.py --train-classification-predictor
python train_pareto_classifier.py --save-analysis
```

### Q: 如何重新训练帕累托分类器？
A: 使用以下命令之一：
```bash
# 方式1：单独训练
python train_pareto_classifier.py --save-analysis

# 方式2：优化时重新训练
python run_classification_nsga3.py --train-pareto-classifier --save-plots
```

### Q: 优化结果不理想怎么办？
A: 尝试调整参数：
```bash
# 增加代数和种群大小
python run_classification_nsga3.py --generations 500 --population-size 200 --save-plots
```

### Q: 如何查看详细的训练和优化过程？
A: 添加--verbose参数：
```bash
python train_pareto_classifier.py --verbose --save-analysis
python run_classification_nsga3.py --verbose --save-plots
```
