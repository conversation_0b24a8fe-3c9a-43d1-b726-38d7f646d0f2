#!/usr/bin/env python3
"""
温度序列稳定性评估器

计算温度序列的稳定性和平滑性指标，用于多目标优化中的第三个目标函数。
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
import logging
from scipy import signal
from scipy.stats import variation

logger = logging.getLogger(__name__)


class SequenceStabilityEvaluator:
    """温度序列稳定性评估器"""
    
    def __init__(self):
        """初始化稳定性评估器"""
        pass
    
    def calculate_stability_score(self, temperature_sequence: np.ndarray) -> float:
        """
        计算温度序列的综合稳定性评分
        
        Args:
            temperature_sequence: 温度序列
            
        Returns:
            稳定性评分（越高越稳定）
        """
        if len(temperature_sequence) < 2:
            return 0.0
        
        # 计算各项稳定性指标
        smoothness = self.calculate_smoothness(temperature_sequence)
        gradient_stability = self.calculate_gradient_stability(temperature_sequence)
        variation_coefficient = self.calculate_variation_coefficient(temperature_sequence)
        trend_consistency = self.calculate_trend_consistency(temperature_sequence)
        
        # 加权综合评分
        stability_score = (
            0.3 * smoothness +
            0.3 * gradient_stability +
            0.2 * (1.0 - variation_coefficient) +  # 变异系数越小越好
            0.2 * trend_consistency
        )
        
        return float(stability_score)
    
    def calculate_smoothness(self, temperature_sequence: np.ndarray) -> float:
        """
        计算温度序列的平滑性
        
        Args:
            temperature_sequence: 温度序列
            
        Returns:
            平滑性评分（0-1，越高越平滑）
        """
        if len(temperature_sequence) < 3:
            return 0.0
        
        # 计算二阶差分（加速度）
        second_diff = np.diff(temperature_sequence, n=2)
        
        # 平滑性 = 1 / (1 + 二阶差分的标准差)
        smoothness = 1.0 / (1.0 + np.std(second_diff))
        
        return float(smoothness)
    
    def calculate_gradient_stability(self, temperature_sequence: np.ndarray) -> float:
        """
        计算温度梯度的稳定性
        
        Args:
            temperature_sequence: 温度序列
            
        Returns:
            梯度稳定性评分（0-1，越高越稳定）
        """
        if len(temperature_sequence) < 2:
            return 0.0
        
        # 计算一阶差分（梯度）
        gradients = np.diff(temperature_sequence)
        
        # 梯度稳定性 = 1 / (1 + 梯度变化的标准差)
        gradient_changes = np.diff(gradients)
        if len(gradient_changes) == 0:
            return 1.0
        
        gradient_stability = 1.0 / (1.0 + np.std(gradient_changes))
        
        return float(gradient_stability)
    
    def calculate_variation_coefficient(self, temperature_sequence: np.ndarray) -> float:
        """
        计算温度序列的变异系数
        
        Args:
            temperature_sequence: 温度序列
            
        Returns:
            变异系数（0-1，越小越稳定）
        """
        if len(temperature_sequence) == 0:
            return 1.0
        
        mean_temp = np.mean(temperature_sequence)
        if mean_temp == 0:
            return 1.0
        
        cv = np.std(temperature_sequence) / abs(mean_temp)
        
        # 归一化到0-1范围
        normalized_cv = min(cv / 2.0, 1.0)  # 假设CV>2为极不稳定
        
        return float(normalized_cv)
    
    def calculate_trend_consistency(self, temperature_sequence: np.ndarray) -> float:
        """
        计算温度趋势的一致性
        
        Args:
            temperature_sequence: 温度序列
            
        Returns:
            趋势一致性评分（0-1，越高越一致）
        """
        if len(temperature_sequence) < 2:
            return 0.0
        
        # 计算梯度
        gradients = np.diff(temperature_sequence)
        
        if len(gradients) == 0:
            return 1.0
        
        # 计算正梯度和负梯度的比例
        positive_gradients = np.sum(gradients > 0)
        negative_gradients = np.sum(gradients < 0)
        total_gradients = len(gradients)
        
        if total_gradients == 0:
            return 1.0
        
        # 趋势一致性 = 主导趋势的比例
        dominant_trend_ratio = max(positive_gradients, negative_gradients) / total_gradients
        
        return float(dominant_trend_ratio)
    
    def calculate_signal_to_noise_ratio(self, temperature_sequence: np.ndarray) -> float:
        """
        计算信噪比
        
        Args:
            temperature_sequence: 温度序列
            
        Returns:
            信噪比（越高越稳定）
        """
        if len(temperature_sequence) < 2:
            return 0.0
        
        # 使用移动平均作为信号
        window_size = min(10, len(temperature_sequence) // 4)
        if window_size < 1:
            window_size = 1
        
        signal_smooth = np.convolve(temperature_sequence, 
                                  np.ones(window_size)/window_size, 
                                  mode='same')
        
        # 噪声 = 原信号 - 平滑信号
        noise = temperature_sequence - signal_smooth
        
        signal_power = np.mean(signal_smooth ** 2)
        noise_power = np.mean(noise ** 2)
        
        if noise_power == 0:
            return float('inf')
        
        snr = signal_power / noise_power
        
        # 归一化SNR到0-1范围
        normalized_snr = min(snr / 100.0, 1.0)  # 假设SNR>100为很好
        
        return float(normalized_snr)
    
    def calculate_local_stability(self, temperature_sequence: np.ndarray, 
                                window_size: int = 50) -> List[float]:
        """
        计算局部稳定性
        
        Args:
            temperature_sequence: 温度序列
            window_size: 窗口大小
            
        Returns:
            局部稳定性评分列表
        """
        if len(temperature_sequence) < window_size:
            return [self.calculate_stability_score(temperature_sequence)]
        
        local_stabilities = []
        
        for i in range(0, len(temperature_sequence) - window_size + 1, window_size // 2):
            window = temperature_sequence[i:i + window_size]
            local_stability = self.calculate_stability_score(window)
            local_stabilities.append(local_stability)
        
        return local_stabilities
    
    def get_stability_metrics(self, temperature_sequence: np.ndarray) -> Dict[str, float]:
        """
        获取所有稳定性指标
        
        Args:
            temperature_sequence: 温度序列
            
        Returns:
            稳定性指标字典
        """
        metrics = {
            'overall_stability': self.calculate_stability_score(temperature_sequence),
            'smoothness': self.calculate_smoothness(temperature_sequence),
            'gradient_stability': self.calculate_gradient_stability(temperature_sequence),
            'variation_coefficient': self.calculate_variation_coefficient(temperature_sequence),
            'trend_consistency': self.calculate_trend_consistency(temperature_sequence),
            'signal_to_noise_ratio': self.calculate_signal_to_noise_ratio(temperature_sequence)
        }
        
        return metrics
    
    def compare_stability(self, sequence1: np.ndarray, sequence2: np.ndarray) -> Dict[str, float]:
        """
        比较两个序列的稳定性
        
        Args:
            sequence1: 第一个温度序列
            sequence2: 第二个温度序列
            
        Returns:
            比较结果字典
        """
        stability1 = self.calculate_stability_score(sequence1)
        stability2 = self.calculate_stability_score(sequence2)
        
        return {
            'sequence1_stability': stability1,
            'sequence2_stability': stability2,
            'stability_difference': stability1 - stability2,
            'better_sequence': 1 if stability1 > stability2 else 2
        }


def main():
    """测试稳定性评估器"""
    evaluator = SequenceStabilityEvaluator()
    
    # 创建测试序列
    # 稳定序列
    stable_sequence = np.linspace(20, 140, 1000) + np.random.normal(0, 1, 1000)
    
    # 不稳定序列
    unstable_sequence = np.random.normal(80, 20, 1000)
    
    # 评估稳定性
    stable_metrics = evaluator.get_stability_metrics(stable_sequence)
    unstable_metrics = evaluator.get_stability_metrics(unstable_sequence)
    
    print("稳定序列指标:")
    for key, value in stable_metrics.items():
        print(f"  {key}: {value:.4f}")
    
    print("\n不稳定序列指标:")
    for key, value in unstable_metrics.items():
        print(f"  {key}: {value:.4f}")
    
    # 比较
    comparison = evaluator.compare_stability(stable_sequence, unstable_sequence)
    print(f"\n比较结果:")
    print(f"  更稳定的序列: {comparison['better_sequence']}")
    print(f"  稳定性差异: {comparison['stability_difference']:.4f}")


if __name__ == "__main__":
    main()
