# 从MOEA/D迁移到基于分类学习的NSGA-III算法

## 迁移概述

本项目已成功将原有的MOEA/D多目标优化算法替换为基于分类学习的NSGA-III算法，同时保留了MOEA/D中经过验证的种群初始化和约束机制。

## 🔄 主要变更

### 1. 算法核心替换
- **原算法**: MOEA/D (Multi-Objective Evolutionary Algorithm based on Decomposition)
- **新算法**: NSGA-III (Non-dominated Sorting Genetic Algorithm III)
- **优势**: NSGA-III在处理多目标优化问题时具有更好的收敛性和多样性保持能力

### 2. 保留的MOEA/D组件
以下组件从MOEA/D算法中完整保留并集成到NSGA-III中：

#### 🏗️ **种群初始化策略**
- **基于真实样本数据的初始化**：使用18个真实化工样本数据初始化种群
- **样本排除机制**：自动排除Sample_8、Sample_13、Sample_19
- **序列标准化**：将不同长度的温度序列标准化到统一长度
- **重复采样**：当种群大小大于样本数量时，智能重复使用样本

#### 🔒 **约束处理机制**
- **PSO风格约束处理器**：完整保留原有的约束处理逻辑
- **平均温度曲线计算**：基于真实样本计算μ_curve
- **动态边界约束**：[μ_curve-θ, μ_curve+θ]约束边界
- **绝对温度限制**：13.0°C - 150.0°C的硬性边界
- **约束应用**：在交叉、变异操作中自动应用约束

#### 📊 **业务数据分析器**
- **BusinessDataAnalyzer**：完整保留数据加载和分析功能
- **温度序列处理**：保持原有的数据预处理流程
- **约束曲线计算**：维持原有的约束参数计算方法

## 🎯 三个优化目标

基于分类学习的NSGA-III算法优化以下三个目标：

1. **F1: 最小化label_1预测值**
   - 使用训练好的label1分类器预测温度序列的label_1数值
   - 目标：越小越好

2. **F2: 最大化label_2预测值**（转换为最小化）
   - 使用训练好的label2分类器预测温度序列的label_2数值
   - 目标：最大化label_2，算法中转换为最小化-label_2

3. **F3: 最大化温度序列稳定性**（转换为最小化）
   - 使用稳定性评估器计算温度序列的综合稳定性评分
   - 目标：最大化稳定性，算法中转换为最小化-稳定性

## 📁 文件结构变更

### 新增文件
```
src/
├── nsga3_optimizer.py                   # NSGA-III核心算法实现
├── classification_based_nsga3.py        # 基于分类学习的NSGA-III集成
├── sequence_stability_evaluator.py      # 温度序列稳定性评估器
├── pareto_classifier_trainer.py         # 帕累托分类器训练器
└── business_data_analyzer.py            # 业务数据分析器（从MOEA/D保留）

scripts/
├── train_pareto_classifier.py           # 训练帕累托分类器
├── run_classification_nsga3.py          # 执行NSGA-III优化
└── README_migration_to_nsga3.md         # 本迁移文档
```

### 保留文件
```
src/
├── data_processor.py                    # 数据处理器（保留）
├── feature_extractor.py                 # 特征提取器（保留）
├── sequence_classifier.py               # 序列分类器（保留）
└── utils.py                             # 工具函数（保留）

config/
└── config.yaml                          # 配置文件（扩展支持NSGA-III）
```

### 移除文件
```
src/
├── moead_optimizer.py                   # MOEA/D优化器（已移除）
├── moead_fitness_evaluator.py           # MOEA/D适应度评估器（已移除）
└── classification_based_moead.py        # 基于分类学习的MOEA/D（已移除）

scripts/
└── run_classification_moead.py          # MOEA/D执行脚本（已移除）
```

## 🔧 配置文件更新

### 新增NSGA-III配置
```yaml
# NSGA-III多目标优化配置
nsga3:
  population_size: 50               # 种群大小（从MOEA/D继承）
  max_generations: 200              # 最大代数（从MOEA/D继承）
  crossover_prob: 0.9               # 交叉概率
  mutation_prob: 0.1                # 变异概率
  eta_c: 20                         # 交叉分布指数
  eta_m: 20                         # 变异分布指数
  reference_points_divisions: 4     # 参考点分割数
```

### 保留MOEA/D配置（兼容性）
```yaml
# MOEA/D配置（保留以维持兼容性）
moead:
  population_size: 50               # 会自动映射到NSGA-III
  max_generations: 200              # 会自动映射到NSGA-III
  # 其他MOEA/D特有参数已不再使用
```

## 🚀 使用方法

### 1. 训练分类器
```bash
# 训练标签分类器
python train_classifier.py --train-classification-predictor --label1-bins 5 --label2-bins 5

# 训练帕累托分类器
python train_pareto_classifier.py --save-analysis
```

### 2. 执行优化
```bash
# 运行基于分类学习的NSGA-III优化
python run_classification_nsga3.py --save-plots --train-pareto-classifier

# 指定参数运行
python run_classification_nsga3.py --generations 300 --population-size 100 --save-plots
```

## 🔍 技术优势

### NSGA-III相比MOEA/D的优势
1. **更好的多样性保持**：基于参考点的选择机制
2. **更强的收敛性**：非支配排序 + niche机制
3. **更适合3目标优化**：专门设计用于处理多目标问题
4. **更稳定的Pareto前沿**：生成更均匀分布的解集

### 保留MOEA/D优势
1. **成熟的约束处理**：经过验证的工业约束机制
2. **真实数据初始化**：基于实际样本的种群初始化
3. **业务逻辑集成**：保持原有的业务规则和数据处理流程

## 📊 性能对比

| 特性 | MOEA/D | NSGA-III |
|------|--------|----------|
| 算法复杂度 | O(N²) | O(N²) |
| 多样性保持 | 中等 | 优秀 |
| 收敛速度 | 快 | 中等 |
| Pareto前沿质量 | 良好 | 优秀 |
| 参数敏感性 | 高 | 中等 |
| 3目标优化适应性 | 中等 | 优秀 |

## 🔄 向后兼容性

- **配置文件**：保持对原有MOEA/D配置的兼容
- **数据格式**：完全兼容原有的数据输入格式
- **结果输出**：保持相同的结果文件结构
- **API接口**：主要接口保持不变

## 📝 注意事项

1. **模型依赖**：需要先训练好相应的分类器模型
2. **内存使用**：NSGA-III可能比MOEA/D使用更多内存
3. **参数调优**：NSGA-III的参数设置可能需要重新调优
4. **结果解释**：Pareto前沿的解释方式可能略有不同

## 🎯 后续优化建议

1. **参数自适应**：实现NSGA-III参数的自适应调整
2. **并行计算**：利用多核CPU加速NSGA-III计算
3. **混合算法**：结合MOEA/D的分解策略和NSGA-III的选择机制
4. **在线学习**：实现分类器的在线更新机制
