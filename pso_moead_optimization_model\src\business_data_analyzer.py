#!/usr/bin/env python3
"""
业务数据分析器模块

该模块负责：
1. 深度分析21个实际温度样本的统计特征
2. 提取温度变化模式和趋势特征
3. 计算阶段性温度特征
4. 为PSO改进提供数据驱动的约束参数
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional
import logging
import os
from pathlib import Path
import yaml
from scipy import stats
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class BusinessDataAnalyzer:
    """业务数据分析器：深度分析实际温度样本特征"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        """
        初始化业务数据分析器
        
        Args:
            config_path: 配置文件路径
        """
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        self.data_config = self.config['data']
        self.data_dir = self.data_config['data_dir']
        
        # 存储分析结果
        self.temperature_sequences = {}
        self.quality_labels = {}
        self.analysis_results = {}
        
        logger.info("业务数据分析器初始化完成")

        # 存储平均温度曲线和约束区间
        self.average_temperature_curve = None
        self.constraint_intervals = None

        # 数据缓存标志
        self._data_loaded = False
        self._last_excluded_samples = None
    
    def load_all_temperature_data(self, excluded_samples=None) -> Dict[int, np.ndarray]:
        """
        加载温度序列数据，支持排除指定样本

        Args:
            excluded_samples: 要排除的样本ID列表，默认排除[8, 13, 19]

        Returns:
            字典，键为样本ID，值为温度序列数组
        """
        if excluded_samples is None:
            excluded_samples = [8, 13, 19]  # 按用户要求排除Sample_8、Sample_13、Sample_19

        # 检查缓存：如果数据已加载且排除样本相同，直接返回缓存数据
        if (self._data_loaded and
            self._last_excluded_samples == excluded_samples and
            self.temperature_sequences):
            logger.info(f"使用缓存的温度序列数据（{len(self.temperature_sequences)}个样本）")
            return self.temperature_sequences

        logger.info("开始加载温度序列数据...")
        logger.info(f"排除样本: {excluded_samples}")
        sequences = {}

        for sample_id in range(1, 22):  # 样本1-21
            # 跳过排除的样本
            if sample_id in excluded_samples:
                logger.info(f"跳过排除的样本 {sample_id}")
                continue

            sample_file = os.path.join(
                self.data_dir,
                self.data_config['sample_file_pattern'].format(sample_id)
            )

            try:
                # 读取Excel文件
                df = pd.read_excel(sample_file, header=None)
                temperature_sequence = df.iloc[:, 0].values

                # 数据清洗：移除NaN值
                temperature_sequence = temperature_sequence[~np.isnan(temperature_sequence)]

                if len(temperature_sequence) > 0:
                    sequences[sample_id] = temperature_sequence
                    logger.info(f"成功加载样本 {sample_id}，序列长度: {len(temperature_sequence):,}")
                else:
                    logger.warning(f"样本 {sample_id} 数据为空")

            except Exception as e:
                logger.error(f"加载样本 {sample_id} 失败: {e}")
                continue

        self.temperature_sequences = sequences

        # 设置缓存标志
        self._data_loaded = True
        self._last_excluded_samples = excluded_samples.copy() if excluded_samples else None

        logger.info(f"总共成功加载了 {len(sequences)} 个温度序列（排除了 {len(excluded_samples)} 个样本）")
        return sequences
    
    def load_quality_labels(self) -> Dict[str, np.ndarray]:
        """
        加载质量标签数据
        
        Returns:
            质量标签字典
        """
        logger.info("加载质量标签数据...")
        labels = {}
        
        # 加载label_1
        label_name = 'label_1'
        filename = self.data_config['label_files'][label_name]
        label_file = os.path.join(self.data_dir, filename)
        
        try:
            df = pd.read_excel(label_file, header=None)
            label_values = df.iloc[:, 0].values
            labels[label_name] = label_values
            
            logger.info(f"成功加载 {label_name}，数量: {len(label_values)}, "
                       f"范围: [{label_values.min():.4f}, {label_values.max():.4f}]")
        except Exception as e:
            logger.error(f"加载标签 {label_name} 失败: {e}")
            raise
        
        self.quality_labels = labels
        return labels
    
    def analyze_basic_statistics(self) -> Dict:
        """
        分析基础统计特征
        
        Returns:
            基础统计特征字典
        """
        logger.info("分析基础统计特征...")
        
        if not self.temperature_sequences:
            raise ValueError("温度序列数据未加载")
        
        stats_results = {
            'sample_count': len(self.temperature_sequences),
            'individual_stats': {},
            'global_stats': {}
        }
        
        # 个体统计
        all_temps = []
        all_lengths = []
        
        for sample_id, sequence in self.temperature_sequences.items():
            sample_stats = {
                'length': len(sequence),
                'mean': np.mean(sequence),
                'std': np.std(sequence),
                'min': np.min(sequence),
                'max': np.max(sequence),
                'median': np.median(sequence),
                'q25': np.percentile(sequence, 25),
                'q75': np.percentile(sequence, 75),
                'range': np.max(sequence) - np.min(sequence),
                'cv': np.std(sequence) / np.mean(sequence) if np.mean(sequence) > 0 else 0
            }
            
            stats_results['individual_stats'][sample_id] = sample_stats
            all_temps.extend(sequence)
            all_lengths.append(len(sequence))
        
        # 全局统计
        all_temps = np.array(all_temps)
        all_lengths = np.array(all_lengths)
        
        stats_results['global_stats'] = {
            'total_data_points': len(all_temps),
            'global_mean': np.mean(all_temps),
            'global_std': np.std(all_temps),
            'global_min': np.min(all_temps),
            'global_max': np.max(all_temps),
            'global_median': np.median(all_temps),
            'length_mean': np.mean(all_lengths),
            'length_std': np.std(all_lengths),
            'length_min': np.min(all_lengths),
            'length_max': np.max(all_lengths),
            'length_median': np.median(all_lengths)
        }
        
        self.analysis_results['basic_statistics'] = stats_results
        logger.info("基础统计特征分析完成")
        return stats_results
    
    def analyze_temperature_trends(self) -> Dict:
        """
        分析温度变化趋势特征
        
        Returns:
            趋势特征字典
        """
        logger.info("分析温度变化趋势特征...")
        
        if not self.temperature_sequences:
            raise ValueError("温度序列数据未加载")
        
        trend_results = {
            'individual_trends': {},
            'global_trend_stats': {}
        }
        
        all_slopes = []
        all_temp_rises = []
        all_start_temps = []
        all_end_temps = []
        
        for sample_id, sequence in self.temperature_sequences.items():
            # 计算线性趋势
            x = np.arange(len(sequence))
            slope, intercept, r_value, p_value, std_err = stats.linregress(x, sequence)
            
            # 计算温度上升幅度
            temp_rise = sequence[-1] - sequence[0]
            start_temp = sequence[0]
            end_temp = sequence[-1]
            
            # 计算变化率统计
            diff = np.diff(sequence)
            
            trend_stats = {
                'slope': slope,
                'intercept': intercept,
                'r_squared': r_value**2,
                'p_value': p_value,
                'temp_rise': temp_rise,
                'start_temp': start_temp,
                'end_temp': end_temp,
                'mean_change_rate': np.mean(diff),
                'std_change_rate': np.std(diff),
                'max_change_rate': np.max(np.abs(diff)),
                'positive_changes': np.sum(diff > 0),
                'negative_changes': np.sum(diff < 0),
                'trend_direction': 'increasing' if slope > 0 else 'decreasing'
            }
            
            trend_results['individual_trends'][sample_id] = trend_stats
            
            all_slopes.append(slope)
            all_temp_rises.append(temp_rise)
            all_start_temps.append(start_temp)
            all_end_temps.append(end_temp)
        
        # 全局趋势统计
        trend_results['global_trend_stats'] = {
            'slope_mean': np.mean(all_slopes),
            'slope_std': np.std(all_slopes),
            'slope_min': np.min(all_slopes),
            'slope_max': np.max(all_slopes),
            'temp_rise_mean': np.mean(all_temp_rises),
            'temp_rise_std': np.std(all_temp_rises),
            'temp_rise_min': np.min(all_temp_rises),
            'temp_rise_max': np.max(all_temp_rises),
            'start_temp_mean': np.mean(all_start_temps),
            'start_temp_std': np.std(all_start_temps),
            'start_temp_min': np.min(all_start_temps),
            'start_temp_max': np.max(all_start_temps),
            'end_temp_mean': np.mean(all_end_temps),
            'end_temp_std': np.std(all_end_temps),
            'end_temp_min': np.min(all_end_temps),
            'end_temp_max': np.max(all_end_temps),
            'increasing_samples': sum(1 for slope in all_slopes if slope > 0),
            'decreasing_samples': sum(1 for slope in all_slopes if slope <= 0)
        }
        
        self.analysis_results['temperature_trends'] = trend_results
        logger.info("温度变化趋势特征分析完成")
        return trend_results

    def analyze_stage_characteristics(self) -> Dict:
        """
        分析阶段性温度特征（起始、中期、结束阶段）

        Returns:
            阶段性特征字典
        """
        logger.info("分析阶段性温度特征...")

        if not self.temperature_sequences:
            raise ValueError("温度序列数据未加载")

        stage_results = {
            'individual_stages': {},
            'global_stage_stats': {}
        }

        # 定义阶段比例
        early_ratio = 0.2   # 前20%为起始阶段
        middle_ratio = 0.6  # 中间60%为中期阶段
        late_ratio = 0.2    # 后20%为结束阶段

        all_early_stats = []
        all_middle_stats = []
        all_late_stats = []

        for sample_id, sequence in self.temperature_sequences.items():
            seq_len = len(sequence)

            # 计算阶段边界
            early_end = int(seq_len * early_ratio)
            middle_end = int(seq_len * (early_ratio + middle_ratio))

            # 分割序列
            early_stage = sequence[:early_end]
            middle_stage = sequence[early_end:middle_end]
            late_stage = sequence[middle_end:]

            # 计算各阶段统计特征
            def stage_stats(stage_seq, stage_name):
                if len(stage_seq) == 0:
                    return {}
                return {
                    f'{stage_name}_mean': np.mean(stage_seq),
                    f'{stage_name}_std': np.std(stage_seq),
                    f'{stage_name}_min': np.min(stage_seq),
                    f'{stage_name}_max': np.max(stage_seq),
                    f'{stage_name}_range': np.max(stage_seq) - np.min(stage_seq),
                    f'{stage_name}_length': len(stage_seq)
                }

            sample_stage_stats = {}
            sample_stage_stats.update(stage_stats(early_stage, 'early'))
            sample_stage_stats.update(stage_stats(middle_stage, 'middle'))
            sample_stage_stats.update(stage_stats(late_stage, 'late'))

            # 计算阶段间变化
            if len(early_stage) > 0 and len(middle_stage) > 0:
                sample_stage_stats['early_to_middle_change'] = np.mean(middle_stage) - np.mean(early_stage)
            if len(middle_stage) > 0 and len(late_stage) > 0:
                sample_stage_stats['middle_to_late_change'] = np.mean(late_stage) - np.mean(middle_stage)
            if len(early_stage) > 0 and len(late_stage) > 0:
                sample_stage_stats['early_to_late_change'] = np.mean(late_stage) - np.mean(early_stage)

            stage_results['individual_stages'][sample_id] = sample_stage_stats

            # 收集全局统计数据
            if len(early_stage) > 0:
                all_early_stats.append({
                    'mean': np.mean(early_stage),
                    'std': np.std(early_stage),
                    'min': np.min(early_stage),
                    'max': np.max(early_stage)
                })

            if len(middle_stage) > 0:
                all_middle_stats.append({
                    'mean': np.mean(middle_stage),
                    'std': np.std(middle_stage),
                    'min': np.min(middle_stage),
                    'max': np.max(middle_stage)
                })

            if len(late_stage) > 0:
                all_late_stats.append({
                    'mean': np.mean(late_stage),
                    'std': np.std(late_stage),
                    'min': np.min(late_stage),
                    'max': np.max(late_stage)
                })

        # 计算全局阶段统计
        def global_stage_stats(stage_stats_list, stage_name):
            if not stage_stats_list:
                return {}

            means = [s['mean'] for s in stage_stats_list]
            stds = [s['std'] for s in stage_stats_list]
            mins = [s['min'] for s in stage_stats_list]
            maxs = [s['max'] for s in stage_stats_list]

            return {
                f'{stage_name}_mean_avg': np.mean(means),
                f'{stage_name}_mean_std': np.std(means),
                f'{stage_name}_mean_min': np.min(means),
                f'{stage_name}_mean_max': np.max(means),
                f'{stage_name}_global_min': np.min(mins),
                f'{stage_name}_global_max': np.max(maxs),
                f'{stage_name}_std_avg': np.mean(stds)
            }

        global_stats = {}
        global_stats.update(global_stage_stats(all_early_stats, 'early'))
        global_stats.update(global_stage_stats(all_middle_stats, 'middle'))
        global_stats.update(global_stage_stats(all_late_stats, 'late'))

        stage_results['global_stage_stats'] = global_stats

        self.analysis_results['stage_characteristics'] = stage_results
        logger.info("阶段性温度特征分析完成")
        return stage_results

    def analyze_change_patterns(self) -> Dict:
        """
        分析温度变化模式

        Returns:
            变化模式字典
        """
        logger.info("分析温度变化模式...")

        if not self.temperature_sequences:
            raise ValueError("温度序列数据未加载")

        pattern_results = {
            'individual_patterns': {},
            'global_pattern_stats': {}
        }

        all_smoothness = []
        all_volatility = []
        all_acceleration = []

        for sample_id, sequence in self.temperature_sequences.items():
            # 计算一阶和二阶差分
            diff1 = np.diff(sequence)
            diff2 = np.diff(sequence, n=2)

            # 平滑性指标
            smoothness = 1.0 / (1.0 + np.var(diff1))

            # 波动性指标
            volatility = np.std(diff1)

            # 加速度指标
            acceleration = np.mean(np.abs(diff2))

            # 变化方向统计
            positive_changes = np.sum(diff1 > 0)
            negative_changes = np.sum(diff1 < 0)
            zero_changes = np.sum(diff1 == 0)

            # 连续性分析
            sign_changes = np.sum(np.diff(np.sign(diff1)) != 0)

            pattern_stats = {
                'smoothness': smoothness,
                'volatility': volatility,
                'acceleration': acceleration,
                'positive_changes': positive_changes,
                'negative_changes': negative_changes,
                'zero_changes': zero_changes,
                'sign_changes': sign_changes,
                'change_ratio': positive_changes / len(diff1) if len(diff1) > 0 else 0,
                'max_positive_change': np.max(diff1[diff1 > 0]) if np.any(diff1 > 0) else 0,
                'max_negative_change': np.min(diff1[diff1 < 0]) if np.any(diff1 < 0) else 0
            }

            pattern_results['individual_patterns'][sample_id] = pattern_stats

            all_smoothness.append(smoothness)
            all_volatility.append(volatility)
            all_acceleration.append(acceleration)

        # 全局模式统计
        pattern_results['global_pattern_stats'] = {
            'smoothness_mean': np.mean(all_smoothness),
            'smoothness_std': np.std(all_smoothness),
            'smoothness_min': np.min(all_smoothness),
            'smoothness_max': np.max(all_smoothness),
            'volatility_mean': np.mean(all_volatility),
            'volatility_std': np.std(all_volatility),
            'volatility_min': np.min(all_volatility),
            'volatility_max': np.max(all_volatility),
            'acceleration_mean': np.mean(all_acceleration),
            'acceleration_std': np.std(all_acceleration),
            'acceleration_min': np.min(all_acceleration),
            'acceleration_max': np.max(all_acceleration)
        }

        self.analysis_results['change_patterns'] = pattern_results
        logger.info("温度变化模式分析完成")
        return pattern_results

    def analyze_quality_correlations(self) -> Dict:
        """
        分析温度特征与质量标签的关联性

        Returns:
            质量关联分析字典
        """
        logger.info("分析温度特征与质量标签的关联性...")

        if not self.temperature_sequences or not self.quality_labels:
            raise ValueError("温度序列数据或质量标签未加载")

        correlation_results = {
            'feature_correlations': {},
            'quality_insights': {}
        }

        # 提取特征向量
        features = []
        quality_values = self.quality_labels['label_1']

        for sample_id in range(1, 22):
            if sample_id in self.temperature_sequences:
                sequence = self.temperature_sequences[sample_id]

                # 提取关键特征
                feature_vector = {
                    'mean_temp': np.mean(sequence),
                    'std_temp': np.std(sequence),
                    'min_temp': np.min(sequence),
                    'max_temp': np.max(sequence),
                    'temp_range': np.max(sequence) - np.min(sequence),
                    'temp_rise': sequence[-1] - sequence[0],
                    'start_temp': sequence[0],
                    'end_temp': sequence[-1],
                    'sequence_length': len(sequence)
                }

                # 添加趋势特征
                x = np.arange(len(sequence))
                slope, _, r_squared, _, _ = stats.linregress(x, sequence)
                feature_vector['slope'] = slope
                feature_vector['r_squared'] = r_squared

                # 添加变化模式特征
                diff1 = np.diff(sequence)
                feature_vector['volatility'] = np.std(diff1)
                feature_vector['smoothness'] = 1.0 / (1.0 + np.var(diff1))

                features.append(feature_vector)

        # 计算特征与质量的相关性
        feature_names = list(features[0].keys())
        correlations = {}

        for feature_name in feature_names:
            feature_values = [f[feature_name] for f in features]

            # 计算皮尔逊相关系数
            corr_coef, p_value = stats.pearsonr(feature_values, quality_values)

            correlations[feature_name] = {
                'correlation': corr_coef,
                'p_value': p_value,
                'significance': 'significant' if p_value < 0.05 else 'not_significant'
            }

        correlation_results['feature_correlations'] = correlations

        # 质量洞察分析
        # 找出高质量和低质量样本的特征差异
        quality_median = np.median(quality_values)
        high_quality_indices = [i for i, q in enumerate(quality_values) if q < quality_median]  # label_1越低越好
        low_quality_indices = [i for i, q in enumerate(quality_values) if q >= quality_median]

        insights = {}
        for feature_name in feature_names:
            high_quality_values = [features[i][feature_name] for i in high_quality_indices]
            low_quality_values = [features[i][feature_name] for i in low_quality_indices]

            insights[feature_name] = {
                'high_quality_mean': np.mean(high_quality_values),
                'high_quality_std': np.std(high_quality_values),
                'low_quality_mean': np.mean(low_quality_values),
                'low_quality_std': np.std(low_quality_values),
                'difference': np.mean(high_quality_values) - np.mean(low_quality_values)
            }

        correlation_results['quality_insights'] = insights

        self.analysis_results['quality_correlations'] = correlation_results
        logger.info("质量关联分析完成")
        return correlation_results

    def generate_constraint_parameters(self) -> Dict:
        """
        基于分析结果生成PSO约束参数

        Returns:
            约束参数字典
        """
        logger.info("生成PSO约束参数...")

        if not self.analysis_results:
            raise ValueError("分析结果不存在，请先运行分析方法")

        # 获取分析结果
        basic_stats = self.analysis_results.get('basic_statistics', {})
        trend_stats = self.analysis_results.get('temperature_trends', {})
        stage_stats = self.analysis_results.get('stage_characteristics', {})
        pattern_stats = self.analysis_results.get('change_patterns', {})

        constraint_params = {
            'temperature_bounds': {},
            'trend_constraints': {},
            'stage_constraints': {},
            'pattern_constraints': {},
            'initialization_params': {}
        }

        # 温度边界约束
        if basic_stats:
            global_stats = basic_stats.get('global_stats', {})
            constraint_params['temperature_bounds'] = {
                'min_temp': global_stats.get('global_min', 13.0),
                'max_temp': global_stats.get('global_max', 152.0),
                'mean_temp_range': [
                    global_stats.get('global_mean', 130.0) - 2 * global_stats.get('global_std', 15.0),
                    global_stats.get('global_mean', 130.0) + 2 * global_stats.get('global_std', 15.0)
                ]
            }

        # 趋势约束
        if trend_stats:
            global_trend = trend_stats.get('global_trend_stats', {})
            constraint_params['trend_constraints'] = {
                'expected_temp_rise_range': [
                    global_trend.get('temp_rise_min', 88.0),
                    global_trend.get('temp_rise_max', 130.0)
                ],
                'start_temp_range': [
                    global_trend.get('start_temp_min', 16.0),
                    global_trend.get('start_temp_max', 32.0)
                ],
                'end_temp_range': [
                    global_trend.get('end_temp_min', 140.0),
                    global_trend.get('end_temp_max', 151.0)
                ],
                'slope_range': [
                    global_trend.get('slope_min', 0.0),
                    global_trend.get('slope_max', 0.01)
                ]
            }

        # 阶段约束
        if stage_stats:
            global_stage = stage_stats.get('global_stage_stats', {})
            constraint_params['stage_constraints'] = {
                'early_stage_range': [
                    global_stage.get('early_global_min', 16.0),
                    global_stage.get('early_global_max', 142.0)
                ],
                'middle_stage_range': [
                    global_stage.get('middle_global_min', 119.0),
                    global_stage.get('middle_global_max', 145.0)
                ],
                'late_stage_range': [
                    global_stage.get('late_global_min', 121.0),
                    global_stage.get('late_global_max', 151.0)
                ]
            }

        # 模式约束
        if pattern_stats:
            global_pattern = pattern_stats.get('global_pattern_stats', {})
            constraint_params['pattern_constraints'] = {
                'max_volatility': global_pattern.get('volatility_max', 5.0),
                'min_smoothness': global_pattern.get('smoothness_min', 0.1),
                'max_acceleration': global_pattern.get('acceleration_max', 2.0)
            }

        # 初始化参数
        constraint_params['initialization_params'] = {
            'use_real_data_seeds': True,
            'seed_sample_ids': list(self.temperature_sequences.keys()),
            'mutation_strength': 0.1,
            'perturbation_ratio': 0.2
        }

        self.analysis_results['constraint_parameters'] = constraint_params
        logger.info("PSO约束参数生成完成")
        return constraint_params

    def run_complete_analysis(self) -> Dict:
        """
        运行完整的数据分析流程

        Returns:
            完整分析结果字典
        """
        logger.info("开始运行完整的数据分析流程...")

        # 加载数据
        self.load_all_temperature_data()
        self.load_quality_labels()

        # 执行各项分析
        self.analyze_basic_statistics()
        self.analyze_temperature_trends()
        self.analyze_stage_characteristics()
        self.analyze_change_patterns()
        self.analyze_quality_correlations()
        self.generate_constraint_parameters()

        logger.info("完整数据分析流程完成")
        return self.analysis_results

    def save_analysis_results(self, output_dir: str = "analysis_results") -> str:
        """
        保存分析结果到文件

        Args:
            output_dir: 输出目录

        Returns:
            保存的文件路径
        """
        logger.info(f"保存分析结果到 {output_dir}...")

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 保存为YAML格式
        output_file = os.path.join(output_dir, "business_data_analysis.yaml")

        with open(output_file, 'w', encoding='utf-8') as f:
            yaml.dump(self.analysis_results, f, default_flow_style=False, allow_unicode=True)

        logger.info(f"分析结果已保存到 {output_file}")
        return output_file

    def generate_analysis_report(self, output_dir: str = "analysis_results") -> str:
        """
        生成分析报告

        Args:
            output_dir: 输出目录

        Returns:
            报告文件路径
        """
        logger.info("生成分析报告...")

        if not self.analysis_results:
            raise ValueError("分析结果不存在，请先运行分析")

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        report_file = os.path.join(output_dir, "business_data_analysis_report.txt")

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("业务数据深度分析报告\n")
            f.write("=" * 80 + "\n\n")

            # 基础统计
            if 'basic_statistics' in self.analysis_results:
                stats = self.analysis_results['basic_statistics']
                f.write("1. 基础统计特征\n")
                f.write("-" * 40 + "\n")
                f.write(f"样本数量: {stats['sample_count']}\n")

                global_stats = stats['global_stats']
                f.write(f"全局温度范围: {global_stats['global_min']:.2f} - {global_stats['global_max']:.2f}°C\n")
                f.write(f"全局平均温度: {global_stats['global_mean']:.2f}°C\n")
                f.write(f"全局温度标准差: {global_stats['global_std']:.2f}°C\n")
                f.write(f"序列长度范围: {global_stats['length_min']:,} - {global_stats['length_max']:,}\n")
                f.write(f"平均序列长度: {global_stats['length_mean']:.0f}\n\n")

            # 趋势特征
            if 'temperature_trends' in self.analysis_results:
                trends = self.analysis_results['temperature_trends']
                f.write("2. 温度变化趋势特征\n")
                f.write("-" * 40 + "\n")

                global_trend = trends['global_trend_stats']
                f.write(f"上升趋势样本: {global_trend['increasing_samples']}/{global_trend['increasing_samples'] + global_trend['decreasing_samples']}\n")
                f.write(f"平均温度上升幅度: {global_trend['temp_rise_mean']:.2f}°C\n")
                f.write(f"温度上升幅度范围: {global_trend['temp_rise_min']:.2f} - {global_trend['temp_rise_max']:.2f}°C\n")
                f.write(f"起始温度范围: {global_trend['start_temp_min']:.2f} - {global_trend['start_temp_max']:.2f}°C\n")
                f.write(f"结束温度范围: {global_trend['end_temp_min']:.2f} - {global_trend['end_temp_max']:.2f}°C\n\n")

            # 阶段特征
            if 'stage_characteristics' in self.analysis_results:
                stages = self.analysis_results['stage_characteristics']
                f.write("3. 阶段性温度特征\n")
                f.write("-" * 40 + "\n")

                global_stage = stages['global_stage_stats']
                f.write(f"起始阶段温度范围: {global_stage.get('early_global_min', 0):.2f} - {global_stage.get('early_global_max', 0):.2f}°C\n")
                f.write(f"中期阶段温度范围: {global_stage.get('middle_global_min', 0):.2f} - {global_stage.get('middle_global_max', 0):.2f}°C\n")
                f.write(f"结束阶段温度范围: {global_stage.get('late_global_min', 0):.2f} - {global_stage.get('late_global_max', 0):.2f}°C\n\n")

            # 变化模式
            if 'change_patterns' in self.analysis_results:
                patterns = self.analysis_results['change_patterns']
                f.write("4. 温度变化模式\n")
                f.write("-" * 40 + "\n")

                global_pattern = patterns['global_pattern_stats']
                f.write(f"平均平滑性: {global_pattern['smoothness_mean']:.4f}\n")
                f.write(f"平均波动性: {global_pattern['volatility_mean']:.4f}\n")
                f.write(f"平均加速度: {global_pattern['acceleration_mean']:.4f}\n\n")

            # 质量关联
            if 'quality_correlations' in self.analysis_results:
                correlations = self.analysis_results['quality_correlations']
                f.write("5. 质量关联分析\n")
                f.write("-" * 40 + "\n")

                feature_corr = correlations['feature_correlations']
                f.write("主要特征与质量的相关性:\n")
                for feature, corr_data in feature_corr.items():
                    f.write(f"  {feature}: {corr_data['correlation']:.4f} ({corr_data['significance']})\n")
                f.write("\n")

            # 约束参数
            if 'constraint_parameters' in self.analysis_results:
                constraints = self.analysis_results['constraint_parameters']
                f.write("6. 推荐的PSO约束参数\n")
                f.write("-" * 40 + "\n")

                temp_bounds = constraints['temperature_bounds']
                f.write(f"温度边界: {temp_bounds['min_temp']:.2f} - {temp_bounds['max_temp']:.2f}°C\n")

                trend_constraints = constraints['trend_constraints']
                f.write(f"期望温度上升范围: {trend_constraints['expected_temp_rise_range'][0]:.2f} - {trend_constraints['expected_temp_rise_range'][1]:.2f}°C\n")
                f.write(f"起始温度范围: {trend_constraints['start_temp_range'][0]:.2f} - {trend_constraints['start_temp_range'][1]:.2f}°C\n")
                f.write(f"结束温度范围: {trend_constraints['end_temp_range'][0]:.2f} - {trend_constraints['end_temp_range'][1]:.2f}°C\n")

        logger.info(f"分析报告已生成: {report_file}")
        return report_file

    def calculate_average_temperature_curve(self, target_length=None) -> np.ndarray:
        """
        计算18个真实样本的平均温度曲线

        Args:
            target_length: 目标序列长度，如果为None则使用最短序列长度

        Returns:
            平均温度曲线数组
        """
        if not self.temperature_sequences:
            logger.error("未加载温度序列数据，无法计算平均曲线")
            return None

        logger.info("计算18个样本的平均温度曲线...")

        # 获取所有序列
        sequences = list(self.temperature_sequences.values())

        if target_length is None:
            # 使用平均序列长度作为目标长度
            lengths = [len(seq) for seq in sequences]
            target_length = int(np.mean(lengths))
            logger.info(f"序列长度统计: 最短={min(lengths)}, 最长={max(lengths)}, 平均={target_length}")

        logger.info(f"目标序列长度: {target_length}")

        # 将所有序列标准化到相同长度
        normalized_sequences = []
        for seq in sequences:
            if len(seq) >= target_length:
                # 等间隔采样到目标长度
                indices = np.linspace(0, len(seq) - 1, target_length, dtype=int)
                normalized_seq = seq[indices]
            else:
                # 如果序列太短，进行插值
                x_old = np.linspace(0, 1, len(seq))
                x_new = np.linspace(0, 1, target_length)
                normalized_seq = np.interp(x_new, x_old, seq)

            normalized_sequences.append(normalized_seq)

        # 计算平均值
        normalized_sequences = np.array(normalized_sequences)
        self.average_temperature_curve = np.mean(normalized_sequences, axis=0)

        logger.info(f"平均温度曲线计算完成，长度: {len(self.average_temperature_curve)}")
        logger.info(f"平均曲线温度范围: {self.average_temperature_curve.min():.2f}°C - {self.average_temperature_curve.max():.2f}°C")

        return self.average_temperature_curve

    def calculate_constraint_intervals(self, theta_method='adaptive') -> Tuple[np.ndarray, np.ndarray]:
        """
        计算约束区间 [平均曲线 - θ, 平均曲线 + θ]

        Args:
            theta_method: θ值计算方法
                - 'adaptive': 每个时间点的标准差
                - 'fixed_std': 固定倍数的整体标准差
                - 'fixed_percent': 固定百分比

        Returns:
            (下边界, 上边界) 元组
        """
        if self.average_temperature_curve is None:
            logger.error("未计算平均温度曲线，无法计算约束区间")
            return None, None

        if not self.temperature_sequences:
            logger.error("未加载温度序列数据，无法计算约束区间")
            return None, None

        logger.info(f"计算约束区间，θ方法: {theta_method}")

        # 获取标准化后的序列（与平均曲线长度一致）
        target_length = len(self.average_temperature_curve)
        sequences = list(self.temperature_sequences.values())

        normalized_sequences = []
        for seq in sequences:
            if len(seq) >= target_length:
                indices = np.linspace(0, len(seq) - 1, target_length, dtype=int)
                normalized_seq = seq[indices]
            else:
                x_old = np.linspace(0, 1, len(seq))
                x_new = np.linspace(0, 1, target_length)
                normalized_seq = np.interp(x_new, x_old, seq)
            normalized_sequences.append(normalized_seq)

        normalized_sequences = np.array(normalized_sequences)

        if theta_method == 'adaptive':
            # 每个时间点的标准差作为θ值
            theta_values = np.std(normalized_sequences, axis=0)
            logger.info(f"自适应θ值范围: {theta_values.min():.2f} - {theta_values.max():.2f}")

        elif theta_method == 'fixed_std':
            # 使用整体标准差的固定倍数
            overall_std = np.std(normalized_sequences)
            # 从配置文件获取倍数，默认2.0
            multiplier = 2.0  # 增加θ值以放宽约束
            theta_values = np.full(target_length, overall_std * multiplier)
            logger.info(f"固定θ值: {theta_values[0]:.2f} (标准差 × {multiplier})")

        elif theta_method == 'fixed_percent':
            # 使用平均曲线的固定百分比
            theta_values = self.average_temperature_curve * 0.1  # 10%
            logger.info(f"百分比θ值范围: {theta_values.min():.2f} - {theta_values.max():.2f}")

        else:
            logger.warning(f"未知的θ方法: {theta_method}，使用自适应方法")
            theta_values = np.std(normalized_sequences, axis=0)

        # 计算约束区间
        lower_bound = self.average_temperature_curve - theta_values
        upper_bound = self.average_temperature_curve + theta_values

        self.constraint_intervals = (lower_bound, upper_bound)

        logger.info(f"约束区间计算完成")
        logger.info(f"下边界范围: {lower_bound.min():.2f}°C - {lower_bound.max():.2f}°C")
        logger.info(f"上边界范围: {upper_bound.min():.2f}°C - {upper_bound.max():.2f}°C")

        return lower_bound, upper_bound


def main():
    """测试业务数据分析器"""
    analyzer = BusinessDataAnalyzer()

    try:
        # 运行完整分析
        results = analyzer.run_complete_analysis()

        # 保存结果
        analyzer.save_analysis_results()

        # 生成报告
        analyzer.generate_analysis_report()

        print("业务数据分析完成！")
        print(f"分析了 {len(analyzer.temperature_sequences)} 个温度样本")

    except Exception as e:
        logger.error(f"分析过程中出错: {e}")
        print(f"分析失败: {e}")


if __name__ == "__main__":
    main()
