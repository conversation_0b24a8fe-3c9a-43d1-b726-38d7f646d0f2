#!/usr/bin/env python3
"""
温度约束处理模块 - 为MOEA/D优化器提供约束处理

基于18个真实数据样本的统计特征构建约束系统，包括：
1. 温度边界约束
2. 统计特征约束（均值、标准差）
3. 阶段性约束（起始、中间、结束阶段）
4. 梯度约束（变化率限制）

集成PSO项目的约束处理机制，确保生成的温度序列符合真实数据特征。
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any
import logging
import yaml
import os

logger = logging.getLogger(__name__)


class TemperatureConstraints:
    """温度约束处理类 - 基于真实数据统计的约束系统"""

    def __init__(self, config_path: str = "config/config.yaml"):
        """
        初始化温度约束处理器

        Args:
            config_path: 配置文件路径
        """
        # 加载配置
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)

        # 约束配置
        constraint_config = self.config.get('constraints', {})
        self.penalty_weight = constraint_config.get('penalty_weight', 1.0)
        self.constraint_enabled = constraint_config.get('enabled', True)

        # 温度范围配置
        temp_config = self.config.get('temperature_sequence', {})
        self.min_temperature = temp_config.get('min_temperature', 13.0)
        self.max_temperature = temp_config.get('max_temperature', 152.0)
        self.sequence_length = temp_config.get('sequence_length', 50000)

        # 基于真实数据的统计约束
        self.real_data_stats = None
        self.average_curve = None
        self.lower_bound_curve = None
        self.upper_bound_curve = None

        # 约束参数
        self.statistical_tolerance = constraint_config.get('statistical_tolerance', 0.2)  # 20%容忍度
        self.gradient_max_change = constraint_config.get('gradient_max_change', 0.5)  # 最大单步变化
        self.smoothness_factor = constraint_config.get('smoothness_factor', 0.95)  # 平滑性要求

        # 初始化约束系统
        self._initialize_constraints()

        logger.info("温度约束处理器初始化完成")
        logger.info(f"温度范围: [{self.min_temperature}, {self.max_temperature}]°C")
        logger.info(f"约束惩罚权重: {self.penalty_weight}")
        logger.info(f"统计容忍度: {self.statistical_tolerance * 100}%")

    def _initialize_constraints(self):
        """初始化约束系统"""
        try:
            # 尝试加载真实数据统计
            self._load_real_data_statistics()
            
            # 如果有真实数据，构建约束曲线
            if self.real_data_stats is not None:
                self._build_constraint_curves()
                
        except Exception as e:
            logger.warning(f"约束系统初始化失败，使用默认约束: {e}")
            self._set_default_constraints()

    def _load_real_data_statistics(self):
        """加载真实数据统计信息"""
        try:
            # 尝试从业务数据分析器获取统计信息
            from .business_data_analyzer import BusinessDataAnalyzer

            analyzer = BusinessDataAnalyzer()

            # 加载数据并运行分析
            analyzer.load_all_temperature_data()
            analyzer.analyze_basic_statistics()

            # 使用配置文件中的序列长度计算平均曲线
            average_curve = analyzer.calculate_average_temperature_curve(target_length=self.sequence_length)

            # 计算约束区间
            lower_bound, upper_bound = analyzer.calculate_constraint_intervals()

            if average_curve is not None and len(average_curve) > 0:
                self.real_data_stats = {
                    'average_curve': average_curve,
                    'lower_bound': lower_bound,
                    'upper_bound': upper_bound,
                    'mean_temperature': np.mean(average_curve),
                    'std_temperature': np.std(average_curve)
                }
                logger.info("成功加载真实数据统计信息")
                logger.info(f"平均温度曲线长度: {len(average_curve)}")
                logger.info(f"温度范围: {average_curve.min():.1f}°C - {average_curve.max():.1f}°C")
            else:
                logger.warning("未能获取平均温度曲线")
                self.real_data_stats = None

        except Exception as e:
            logger.warning(f"加载真实数据统计失败: {e}")
            self.real_data_stats = None

    def _build_constraint_curves(self):
        """基于真实数据构建约束曲线"""
        if self.real_data_stats is None:
            return

        try:
            # 获取统计信息
            stats = self.real_data_stats

            # 使用业务数据分析器计算的平均温度曲线
            if 'average_curve' in stats:
                original_curve = stats['average_curve']

                # 调整曲线长度以匹配目标序列长度
                if len(original_curve) != self.sequence_length:
                    x_old = np.linspace(0, 1, len(original_curve))
                    x_new = np.linspace(0, 1, self.sequence_length)
                    self.average_curve = np.interp(x_new, x_old, original_curve)
                else:
                    self.average_curve = original_curve.copy()
            else:
                # 如果没有平均曲线，使用全局平均值
                mean_temp = stats.get('mean_temperature', 100.0)
                self.average_curve = np.full(self.sequence_length, mean_temp)

            # 使用业务数据分析器计算的约束边界
            if 'lower_bound' in stats and 'upper_bound' in stats:
                original_lower = stats['lower_bound']
                original_upper = stats['upper_bound']

                # 调整边界长度以匹配目标序列长度
                if len(original_lower) != self.sequence_length:
                    x_old = np.linspace(0, 1, len(original_lower))
                    x_new = np.linspace(0, 1, self.sequence_length)
                    self.lower_bound_curve = np.interp(x_new, x_old, original_lower)
                    self.upper_bound_curve = np.interp(x_new, x_old, original_upper)
                else:
                    self.lower_bound_curve = original_lower.copy()
                    self.upper_bound_curve = original_upper.copy()
            else:
                # 如果没有预计算的边界，使用标准差构建
                std_temp = stats.get('std_temperature', 20.0)
                self.lower_bound_curve = self.average_curve - 2.0 * std_temp
                self.upper_bound_curve = self.average_curve + 2.0 * std_temp

            # 确保边界在全局温度范围内
            self.lower_bound_curve = np.clip(self.lower_bound_curve,
                                           self.min_temperature, self.max_temperature)
            self.upper_bound_curve = np.clip(self.upper_bound_curve,
                                           self.min_temperature, self.max_temperature)

            logger.info("基于18个样本的约束曲线构建完成")
            logger.info(f"平均温度范围: [{self.average_curve.min():.1f}, {self.average_curve.max():.1f}]°C")
            logger.info(f"约束边界范围: [{self.lower_bound_curve.min():.1f}, {self.upper_bound_curve.max():.1f}]°C")
            logger.info(f"约束曲线长度: {len(self.average_curve)}")

        except Exception as e:
            logger.error(f"约束曲线构建失败: {e}")
            self._set_default_constraints()

    def _set_default_constraints(self):
        """设置默认约束参数"""
        # 构建简单的线性约束曲线
        self.average_curve = np.linspace(
            (self.min_temperature + self.max_temperature) / 2,
            (self.min_temperature + self.max_temperature) / 2,
            self.sequence_length
        )
        
        # 设置较宽的约束边界
        margin = (self.max_temperature - self.min_temperature) * 0.1
        self.lower_bound_curve = np.full(self.sequence_length, self.min_temperature + margin)
        self.upper_bound_curve = np.full(self.sequence_length, self.max_temperature - margin)

        logger.info("使用默认约束参数")

    def apply_constraints(self, temperature_sequence: np.ndarray) -> np.ndarray:
        """
        应用温度约束

        Args:
            temperature_sequence: 输入温度序列

        Returns:
            约束后的温度序列
        """
        if not self.constraint_enabled:
            return temperature_sequence

        constrained_sequence = temperature_sequence.copy()

        # 1. 应用基本温度边界约束
        constrained_sequence = self._apply_temperature_bounds(constrained_sequence)

        # 2. 应用基于真实数据的曲线约束
        if self.lower_bound_curve is not None and self.upper_bound_curve is not None:
            constrained_sequence = self._apply_curve_constraints(constrained_sequence)

        # 3. 应用梯度约束
        constrained_sequence = self._apply_gradient_constraints(constrained_sequence)

        # 4. 应用平滑性约束
        constrained_sequence = self._apply_smoothness_constraints(constrained_sequence)

        return constrained_sequence

    def _apply_temperature_bounds(self, sequence: np.ndarray) -> np.ndarray:
        """应用基本温度边界约束"""
        return np.clip(sequence, self.min_temperature, self.max_temperature)

    def _apply_curve_constraints(self, sequence: np.ndarray) -> np.ndarray:
        """应用基于真实数据的曲线约束"""
        # 调整序列长度以匹配约束曲线
        if len(sequence) != len(self.lower_bound_curve):
            x_old = np.linspace(0, 1, len(sequence))
            x_new = np.linspace(0, 1, len(self.lower_bound_curve))
            sequence = np.interp(x_new, x_old, sequence)

        # 应用曲线边界约束
        constrained_sequence = np.clip(sequence, self.lower_bound_curve, self.upper_bound_curve)

        # 调整回原始长度
        if len(constrained_sequence) != self.sequence_length:
            x_old = np.linspace(0, 1, len(constrained_sequence))
            x_new = np.linspace(0, 1, self.sequence_length)
            constrained_sequence = np.interp(x_new, x_old, constrained_sequence)

        return constrained_sequence

    def _apply_gradient_constraints(self, sequence: np.ndarray) -> np.ndarray:
        """应用梯度约束"""
        constrained_sequence = sequence.copy()
        
        # 计算梯度
        gradients = np.diff(constrained_sequence)
        
        # 限制梯度幅度
        max_gradient = self.gradient_max_change
        clipped_gradients = np.clip(gradients, -max_gradient, max_gradient)
        
        # 重构序列
        constrained_sequence[1:] = constrained_sequence[0] + np.cumsum(clipped_gradients)
        
        return constrained_sequence

    def _apply_smoothness_constraints(self, sequence: np.ndarray) -> np.ndarray:
        """应用平滑性约束"""
        # 使用移动平均进行平滑
        window_size = max(3, len(sequence) // 1000)  # 自适应窗口大小
        
        # 计算移动平均
        smoothed = np.convolve(sequence, np.ones(window_size)/window_size, mode='same')
        
        # 混合原始序列和平滑序列
        alpha = self.smoothness_factor
        constrained_sequence = alpha * smoothed + (1 - alpha) * sequence
        
        return constrained_sequence

    def calculate_constraint_penalty(self, temperature_sequence: np.ndarray) -> float:
        """
        计算约束违反惩罚

        Args:
            temperature_sequence: 温度序列

        Returns:
            约束惩罚值
        """
        if not self.constraint_enabled:
            return 0.0

        penalty = 0.0

        # 1. 温度边界违反惩罚
        boundary_violations = np.sum(
            (temperature_sequence < self.min_temperature) | 
            (temperature_sequence > self.max_temperature)
        )
        penalty += boundary_violations * 0.1

        # 2. 曲线约束违反惩罚
        if self.lower_bound_curve is not None and self.upper_bound_curve is not None:
            # 调整序列长度以匹配约束曲线
            if len(temperature_sequence) != len(self.lower_bound_curve):
                x_old = np.linspace(0, 1, len(temperature_sequence))
                x_new = np.linspace(0, 1, len(self.lower_bound_curve))
                adjusted_sequence = np.interp(x_new, x_old, temperature_sequence)
            else:
                adjusted_sequence = temperature_sequence

            curve_violations = np.sum(
                (adjusted_sequence < self.lower_bound_curve) | 
                (adjusted_sequence > self.upper_bound_curve)
            )
            penalty += curve_violations * 0.05

        # 3. 梯度违反惩罚
        gradients = np.abs(np.diff(temperature_sequence))
        gradient_violations = np.sum(gradients > self.gradient_max_change)
        penalty += gradient_violations * 0.02

        # 4. 统计特征违反惩罚
        if self.real_data_stats is not None:
            penalty += self._calculate_statistical_penalty(temperature_sequence)

        return penalty * self.penalty_weight

    def _calculate_statistical_penalty(self, temperature_sequence: np.ndarray) -> float:
        """计算统计特征违反惩罚"""
        penalty = 0.0

        try:
            # 计算当前序列统计特征
            current_mean = np.mean(temperature_sequence)
            current_std = np.std(temperature_sequence)

            # 获取目标统计特征
            target_mean = self.real_data_stats.get('mean_temperature', current_mean)
            target_std = self.real_data_stats.get('std_temperature', current_std)

            # 计算统计偏差惩罚
            mean_deviation = abs(current_mean - target_mean) / target_mean
            std_deviation = abs(current_std - target_std) / target_std

            if mean_deviation > self.statistical_tolerance:
                penalty += (mean_deviation - self.statistical_tolerance) * 2.0

            if std_deviation > self.statistical_tolerance:
                penalty += (std_deviation - self.statistical_tolerance) * 1.0

        except Exception as e:
            logger.debug(f"统计惩罚计算失败: {e}")

        return penalty

    def get_constraint_info(self) -> Dict[str, Any]:
        """
        获取约束系统信息

        Returns:
            约束信息字典
        """
        return {
            'constraint_enabled': self.constraint_enabled,
            'temperature_range': [self.min_temperature, self.max_temperature],
            'penalty_weight': self.penalty_weight,
            'statistical_tolerance': self.statistical_tolerance,
            'gradient_max_change': self.gradient_max_change,
            'smoothness_factor': self.smoothness_factor,
            'has_real_data_stats': self.real_data_stats is not None,
            'has_constraint_curves': self.lower_bound_curve is not None,
            'sequence_length': self.sequence_length
        }
