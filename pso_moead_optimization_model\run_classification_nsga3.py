#!/usr/bin/env python3
"""
基于分类学习的NSGA-III优化主程序

该脚本实现基于分类学习的多目标优化：
1. 加载训练好的分类器
2. 定义基于分类的三个目标函数
3. 执行NSGA-III优化
4. 输出优化结果

目标函数：
- f1: 最小化label_1预测值
- f2: 最大化label_2预测值（转换为最小化）
- f3: 最大化温度序列稳定性（转换为最小化）
"""

import os
import sys
import argparse
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
import json

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.classification_based_nsga3 import ClassificationBasedNSGA3
from src.utils import load_config, setup_logging, create_directories, print_system_info


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='基于分类学习的NSGA-III多目标优化')
    
    parser.add_argument('--config', type=str, default='config/config.yaml',
                       help='配置文件路径')
    parser.add_argument('--models-dir', type=str, default='models',
                       help='训练好的模型目录')
    parser.add_argument('--output-dir', type=str, default='results',
                       help='结果输出目录')
    parser.add_argument('--generations', type=int, default=None,
                       help='优化代数（覆盖配置文件设置）')
    parser.add_argument('--population-size', type=int, default=None,
                       help='种群大小（覆盖配置文件设置）')
    parser.add_argument('--train-pareto-classifier', action='store_true',
                       help='重新训练帕累托分类器（如果已训练好则不需要此参数）')
    parser.add_argument('--save-plots', action='store_true',
                       help='保存优化结果图表')
    parser.add_argument('--verbose', action='store_true',
                       help='详细输出模式')
    
    return parser.parse_args()


def validate_models(models_dir: str, logger=None) -> bool:
    """
    验证必要的模型文件是否存在

    Args:
        models_dir: 模型目录
        logger: 日志记录器

    Returns:
        是否所有必要模型都存在
    """
    # 帕累托分类器核心文件（必需）
    required_files = [
        'feature_extractor.joblib',
        'pareto_sequence_classifier.joblib',
        'pareto_sequence_scaler.joblib'
    ]

    # 标签分类器文件（可选，用于目标函数记录）
    label_files = [
        'label1_classifier.joblib',
        'label2_classifier.joblib'
    ]

    missing_required = []
    missing_label = []

    # 检查必要文件（帕累托分类器核心文件）
    for filename in required_files:
        filepath = os.path.join(models_dir, filename)
        if not os.path.exists(filepath):
            missing_required.append(filename)

    # 检查标签分类器文件（可选）
    for filename in label_files:
        filepath = os.path.join(models_dir, filename)
        if not os.path.exists(filepath):
            missing_label.append(filename)

    # 检查必要文件
    if missing_required:
        if logger:
            logger.error(f"缺少必要的帕累托分类器文件: {missing_required}")
            logger.error("请先运行: python train_pareto_classifier.py --save-analysis")
        else:
            print(f"❌ 缺少必要的帕累托分类器文件: {missing_required}")
            print("请先运行: python train_pareto_classifier.py --save-analysis")
        return False

    # 报告缺失的标签分类器文件（不影响运行）
    if missing_label:
        if logger:
            logger.debug(f"标签分类器文件不存在: {missing_label}")
            logger.debug("将使用简单统计特征作为目标函数替代")
        # 不在控制台显示，因为这是正常情况

    return True


def save_results(results: dict, output_dir: str, timestamp: str, logger=None):
    """
    保存优化结果

    Args:
        results: 优化结果字典
        output_dir: 输出目录
        timestamp: 时间戳
        logger: 日志记录器
    """
    # 保存JSON结果
    json_path = os.path.join(output_dir, f"classification_nsga3_results_{timestamp}.json")
    
    # 转换numpy数组为列表以便JSON序列化
    serializable_results = {}
    for key, value in results.items():
        if isinstance(value, np.ndarray):
            serializable_results[key] = value.tolist()
        elif isinstance(value, list) and len(value) > 0:
            if hasattr(value[0], 'objectives'):
                # Individual对象列表
                serializable_results[key] = [
                    {
                        'decision_variables': ind.decision_variables.tolist(),
                        'objectives': ind.objectives.tolist() if ind.objectives is not None else None
                    }
                    for ind in value
                ]
            elif isinstance(value[0], np.ndarray):
                serializable_results[key] = [v.tolist() for v in value]
            else:
                serializable_results[key] = value
        else:
            serializable_results[key] = value
    
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(serializable_results, f, indent=2, ensure_ascii=False)

    if logger:
        logger.info(f"优化结果已保存到 {json_path}")
    else:
        print(f"✅ 优化结果已保存到 {json_path}")

    # 保存Pareto前沿
    if 'pareto_front' in results and len(results['pareto_front']) > 0:
        pareto_df = pd.DataFrame(results['pareto_front'],
                                columns=['f1_label1', 'f2_neg_label2', 'f3_neg_stability'])
        pareto_path = os.path.join(output_dir, f"classification_nsga3_pareto_front_{timestamp}.csv")
        pareto_df.to_csv(pareto_path, index=False)
        if logger:
            logger.info(f"Pareto前沿已保存到 {pareto_path}")
        else:
            print(f"✅ Pareto前沿已保存到 {pareto_path}")

    # 保存最优解的温度序列
    if 'best_solutions' in results and len(results['best_solutions']) > 0:
        best_solutions_data = []
        for i, solution in enumerate(results['best_solutions']):
            solution_data = {
                'solution_id': i,
                'temperature_sequence': solution.decision_variables.tolist(),
                'objectives': solution.objectives.tolist() if solution.objectives is not None else None
            }
            best_solutions_data.append(solution_data)

        best_path = os.path.join(output_dir, f"classification_nsga3_best_solutions_{timestamp}.json")
        with open(best_path, 'w', encoding='utf-8') as f:
            json.dump(best_solutions_data, f, indent=2, ensure_ascii=False)
        if logger:
            logger.info(f"最优解已保存到 {best_path}")
        else:
            print(f"✅ 最优解已保存到 {best_path}")


def plot_results(results: dict, output_dir: str, timestamp: str, logger=None):
    """
    绘制优化结果图表

    Args:
        results: 优化结果字典
        output_dir: 输出目录
        timestamp: 时间戳
        logger: 日志记录器
    """
    plt.style.use('default')
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建子图
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('基于分类学习的NSGA-III优化结果', fontsize=16, fontweight='bold')
    
    # 1. Pareto前沿3D散点图
    if 'pareto_front' in results and len(results['pareto_front']) > 0:
        ax1 = fig.add_subplot(2, 2, 1, projection='3d')
        pareto_front = np.array(results['pareto_front'])
        
        scatter = ax1.scatter(pareto_front[:, 0], pareto_front[:, 1], pareto_front[:, 2], 
                            c=pareto_front[:, 2], cmap='viridis', s=50, alpha=0.7)
        ax1.set_xlabel('F1: Label1预测值')
        ax1.set_ylabel('F2: -Label2预测值')
        ax1.set_zlabel('F3: -稳定性评分')
        ax1.set_title('Pareto前沿')
        plt.colorbar(scatter, ax=ax1, shrink=0.5)
    else:
        axes[0, 0].text(0.5, 0.5, '无Pareto前沿数据', ha='center', va='center')
        axes[0, 0].set_title('Pareto前沿')
    
    # 2. 收敛历史
    if 'generation_history' in results and len(results['generation_history']) > 0:
        generations = [gen['generation'] for gen in results['generation_history']]
        mean_f1 = [gen['mean_objectives'][0] for gen in results['generation_history']]
        mean_f2 = [gen['mean_objectives'][1] for gen in results['generation_history']]
        mean_f3 = [gen['mean_objectives'][2] for gen in results['generation_history']]
        
        axes[0, 1].plot(generations, mean_f1, label='F1: Label1', alpha=0.8)
        axes[0, 1].plot(generations, mean_f2, label='F2: -Label2', alpha=0.8)
        axes[0, 1].plot(generations, mean_f3, label='F3: -稳定性', alpha=0.8)
        axes[0, 1].set_xlabel('代数')
        axes[0, 1].set_ylabel('平均目标函数值')
        axes[0, 1].set_title('收敛历史')
        axes[0, 1].legend()
        axes[0, 1].grid(True)
    else:
        axes[0, 1].text(0.5, 0.5, '无收敛历史数据', ha='center', va='center')
        axes[0, 1].set_title('收敛历史')
    
    # 3. 目标函数分布
    if 'pareto_front' in results and len(results['pareto_front']) > 0:
        pareto_front = np.array(results['pareto_front'])
        
        axes[1, 0].hist(pareto_front[:, 0], bins=15, alpha=0.7, label='F1: Label1预测值', color='red')
        axes[1, 0].hist(pareto_front[:, 1], bins=15, alpha=0.7, label='F2: -Label2预测值', color='green')
        axes[1, 0].hist(pareto_front[:, 2], bins=15, alpha=0.7, label='F3: -稳定性评分', color='blue')
        axes[1, 0].set_xlabel('目标函数值')
        axes[1, 0].set_ylabel('频次')
        axes[1, 0].set_title('目标函数值分布')
        axes[1, 0].legend()
    else:
        axes[1, 0].text(0.5, 0.5, '无目标函数分布数据', ha='center', va='center')
        axes[1, 0].set_title('目标函数值分布')
    
    # 4. 最优解温度曲线
    if 'best_solutions' in results and len(results['best_solutions']) > 0:
        # 显示前5个最优解的温度曲线
        for i, solution in enumerate(results['best_solutions'][:5]):
            axes[1, 1].plot(solution.decision_variables, 
                          label=f'解{i+1}', alpha=0.8, linewidth=1.5)
        
        axes[1, 1].set_xlabel('时间步')
        axes[1, 1].set_ylabel('温度 (°C)')
        axes[1, 1].set_title('最优温度序列')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
    else:
        axes[1, 1].text(0.5, 0.5, '无最优解数据', ha='center', va='center')
        axes[1, 1].set_title('最优温度序列')
    
    plt.tight_layout()
    
    # 保存图表
    plot_path = os.path.join(output_dir, f"classification_nsga3_results_{timestamp}.png")
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    if logger:
        logger.info(f"结果图表已保存到 {plot_path}")
    else:
        print(f"✅ 结果图表已保存到 {plot_path}")

    plt.show()


def main():
    """主函数"""
    # 解析参数
    args = parse_arguments()
    
    # 打印系统信息
    print_system_info()
    
    # 初始化logger为None，以便在异常处理中检查
    logger = None
    
    try:
        # 加载配置
        config = load_config(args.config)
        
        # 设置日志
        logger = setup_logging(config)
        logger.info("开始基于分类学习的NSGA-III优化")
        
        # 创建输出目录
        create_directories([args.output_dir])
        
        # 应用命令行参数覆盖配置（同时更新MOEAD和NSGA3配置）
        if args.generations is not None:
            # 更新MOEAD配置（保持兼容性）
            if 'moead' not in config:
                config['moead'] = {}
            config['moead']['max_generations'] = args.generations

            # 更新NSGA3配置
            if 'nsga3' not in config:
                config['nsga3'] = {}
            config['nsga3']['max_generations'] = args.generations
            logger.info(f"优化代数设置为: {args.generations}")

        if args.population_size is not None:
            # 更新MOEAD配置（保持兼容性）
            if 'moead' not in config:
                config['moead'] = {}
            config['moead']['population_size'] = args.population_size

            # 更新NSGA3配置
            if 'nsga3' not in config:
                config['nsga3'] = {}
            config['nsga3']['population_size'] = args.population_size
            logger.info(f"种群大小设置为: {args.population_size}")
        
        # 初始化基于分类学习的NSGA-III优化器
        logger.info("初始化基于分类学习的NSGA-III优化器...")
        optimizer = ClassificationBasedNSGA3(args.config)
        
        # 训练帕累托分类器（仅在指定参数时）
        if args.train_pareto_classifier:
            logger.info("重新训练帕累托分类器...")
            training_results = optimizer.train_pareto_classifier()
            logger.info("帕累托分类器训练完成")
            logger.info(f"  优秀样本数量: {training_results['analysis_results']['pareto_results']['excellent_count']}")
            logger.info(f"  较差样本数量: {training_results['analysis_results']['pareto_results']['poor_count']}")
            logger.info(f"  分类器准确率: {training_results['training_results']['cv_accuracy_mean']:.4f}")
        else:
            logger.info("跳过帕累托分类器训练，将直接加载已训练的模型")
        
        # 验证模型文件
        if not validate_models(args.models_dir, logger):
            logger.error("模型文件验证失败，请先训练相关分类器")
            return False
        
        # 加载训练好的分类器
        logger.info("加载训练好的分类器...")
        optimizer.load_trained_classifiers(args.models_dir)
        
        # 执行优化
        logger.info("开始执行基于分类学习的NSGA-III优化...")
        start_time = datetime.now()
        
        results = optimizer.optimize()
        
        end_time = datetime.now()
        total_time = (end_time - start_time).total_seconds()
        
        # 添加运行信息
        results['run_info'] = {
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'total_time': total_time,
            'config_path': args.config,
            'models_dir': args.models_dir,
            'output_dir': args.output_dir
        }
        
        logger.info("基于分类学习的NSGA-III优化完成")
        logger.info(f"总优化时间: {total_time:.2f}秒")
        
        if 'pareto_front' in results:
            logger.info(f"Pareto前沿解数量: {len(results['pareto_front'])}")
        
        if 'pareto_front_size' in results:
            logger.info(f"Pareto前沿大小: {results['pareto_front_size']}")
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        save_results(results, args.output_dir, timestamp, logger)

        # 绘制结果图表
        if args.save_plots:
            plot_results(results, args.output_dir, timestamp, logger)
        
        logger.info("=" * 50)
        logger.info("基于分类学习的NSGA-III优化完成！")
        logger.info("=" * 50)
        logger.info(f"结果文件保存在: {args.output_dir}")
        
        return True
        
    except Exception as e:
        # 检查logger是否已初始化
        if logger is not None:
            logger.error(f"优化过程中发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())
        else:
            # 如果logger未初始化，使用print输出错误
            print(f"❌ 优化过程中发生错误: {e}")
            import traceback
            print("详细错误信息:")
            traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
