2025-07-30 14:57:17,023 - optimization_system - INFO - 开始训练温度序列比较分类器
2025-07-30 14:57:17,025 - optimization_system - INFO - ==================================================
2025-07-30 14:57:17,026 - optimization_system - INFO - 步骤1: 加载和处理数据
2025-07-30 14:57:17,026 - optimization_system - INFO - ==================================================
2025-07-30 14:59:24,404 - optimization_system - INFO - 启用数据增强...
2025-07-30 14:59:24,935 - optimization_system - INFO - 数据集统计信息:
2025-07-30 14:59:24,935 - optimization_system - INFO -   sample_count: 21
2025-07-30 14:59:24,935 - optimization_system - INFO -   sequence_length: {'min': 18288, 'max': 50000, 'mean': 29522.619047619046, 'std': 9540.01208682568}
2025-07-30 14:59:24,945 - optimization_system - INFO -   temperature_range: {'min': 95.1, 'max': 150.9, 'mean': 134.65848542279932, 'std': 6.278435212711021}
2025-07-30 14:59:24,946 - optimization_system - INFO -   quality_scores: {'min': 0.0, 'max': 1.0, 'mean': 0.49659863945578225, 'std': 0.29283324149701884}
2025-07-30 14:59:24,946 - optimization_system - INFO -   pairwise_samples: 210
2025-07-30 14:59:24,946 - optimization_system - INFO - ==================================================
2025-07-30 14:59:24,947 - optimization_system - INFO - 步骤2: 特征提取
2025-07-30 14:59:24,947 - optimization_system - INFO - ==================================================
2025-07-30 14:59:34,171 - optimization_system - INFO - 特征提取完成:
2025-07-30 14:59:34,171 - optimization_system - INFO -   特征矩阵形状: (420, 407)
2025-07-30 14:59:34,171 - optimization_system - INFO -   特征数量: 407
2025-07-30 14:59:34,171 - optimization_system - INFO -   样本数量: 420
2025-07-30 14:59:34,172 - optimization_system - INFO -   标签分布: 类别0=270, 类别1=150
2025-07-30 14:59:34,172 - optimization_system - INFO - ==================================================
2025-07-30 14:59:34,172 - optimization_system - INFO - 步骤3: 训练分类器
2025-07-30 14:59:34,172 - optimization_system - INFO - ==================================================
2025-07-30 14:59:34,738 - optimization_system - INFO - 训练结果:
2025-07-30 14:59:34,738 - optimization_system - INFO -   训练准确率: 0.8750
2025-07-30 14:59:34,738 - optimization_system - INFO -   测试准确率: 0.8333
2025-07-30 14:59:34,739 - optimization_system - INFO -   交叉验证准确率: 0.8124 ± 0.0432
2025-07-30 14:59:34,739 - optimization_system - INFO -   测试AUC: 0.9136
2025-07-30 14:59:34,739 - optimization_system - INFO - ==================================================
2025-07-30 14:59:34,740 - optimization_system - INFO - 步骤3.5: 训练分类预测器和帕累托分析
2025-07-30 14:59:34,740 - optimization_system - INFO - ==================================================
2025-07-30 14:59:34,740 - optimization_system - INFO - 训练基于分类学习的标签预测器...
2025-07-30 15:01:33,434 - optimization_system - INFO - 帕累托分类器训练结果:
2025-07-30 15:01:33,435 - optimization_system - INFO -   分类器准确率: 0.9100 ± 0.1114
2025-07-30 15:01:33,435 - optimization_system - INFO -   特征数量: 101
2025-07-30 15:01:33,435 - optimization_system - INFO -   训练样本数量: 21
2025-07-30 15:01:33,435 - optimization_system - INFO -   优秀样本数量: 19
2025-07-30 15:01:33,435 - optimization_system - INFO -   较差样本数量: 2
2025-07-30 15:01:33,436 - optimization_system - INFO - ==================================================
2025-07-30 15:01:33,436 - optimization_system - INFO - 步骤4: 保存模型
2025-07-30 15:01:33,436 - optimization_system - INFO - ==================================================
2025-07-30 15:01:33,607 - optimization_system - INFO - 特征提取器已保存到 models\feature_extractor.joblib
2025-07-30 15:01:33,615 - optimization_system - INFO - 分类预测器已保存到 models\classification_predictor.joblib
2025-07-30 15:01:33,616 - optimization_system - INFO - 帕累托分类器已自动保存到models目录
2025-07-30 15:01:33,616 - optimization_system - INFO -   - pareto_sequence_classifier.joblib: 帕累托分类器
2025-07-30 15:01:33,616 - optimization_system - INFO -   - pareto_sequence_scaler.joblib: 特征标准化器
2025-07-30 15:01:33,616 - optimization_system - INFO -   - pareto_classification.joblib: 样本分类结果
2025-07-30 15:01:33,617 - optimization_system - INFO - 训练摘要已保存到 models\training_summary_20250730_150133.txt
2025-07-30 15:01:33,618 - optimization_system - INFO - ==================================================
2025-07-30 15:01:33,618 - optimization_system - INFO - 步骤6: 模型验证
2025-07-30 15:01:33,618 - optimization_system - INFO - ==================================================
2025-07-30 15:01:33,681 - optimization_system - INFO - 验证结果:
2025-07-30 15:01:33,681 - optimization_system - INFO -   准确率: 0.8667
2025-07-30 15:01:33,681 - optimization_system - INFO -   AUC: 0.9491
2025-07-30 15:01:33,683 - optimization_system - INFO - ==================================================
2025-07-30 15:01:33,683 - optimization_system - INFO - 分类器训练完成！
2025-07-30 15:01:33,683 - optimization_system - INFO - ==================================================
2025-07-30 15:01:33,684 - optimization_system - INFO - 模型文件保存在: models
2025-07-30 15:01:33,684 - optimization_system - INFO - 训练摘要保存在: models\training_summary_20250730_150133.txt
2025-07-30 15:08:02,672 - optimization_system - INFO - 开始基于分类学习的NSGA-III优化
2025-07-30 15:08:02,672 - optimization_system - INFO - 初始化基于分类学习的NSGA-III优化器...
2025-07-30 15:08:02,758 - optimization_system - INFO - 训练帕累托分类器...
2025-07-30 15:10:14,029 - optimization_system - INFO - 帕累托分类器训练完成
2025-07-30 15:10:14,029 - optimization_system - INFO -   优秀样本数量: 19
2025-07-30 15:10:14,030 - optimization_system - INFO -   较差样本数量: 2
2025-07-30 15:10:14,030 - optimization_system - INFO -   分类器准确率: 0.9100
2025-07-30 15:10:14,031 - optimization_system - ERROR - 优化过程中发生错误: name 'logger' is not defined
2025-07-30 15:10:14,032 - optimization_system - ERROR - Traceback (most recent call last):
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\run_classification_nsga3.py", line 321, in main
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\run_classification_nsga3.py", line 97, in validate_models
NameError: name 'logger' is not defined

2025-07-30 15:17:17,506 - optimization_system - INFO - 开始基于分类学习的NSGA-III优化
2025-07-30 15:17:17,507 - optimization_system - INFO - 初始化基于分类学习的NSGA-III优化器...
2025-07-30 15:17:17,569 - optimization_system - INFO - 尝试加载已训练好的帕累托分类器...
2025-07-30 15:17:17,569 - optimization_system - INFO - 找到已训练的帕累托分类器，将直接使用
2025-07-30 15:17:17,570 - optimization_system - WARNING - 缺少可选的模型文件: ['label1_classifier.joblib', 'label2_classifier.joblib']
2025-07-30 15:17:17,570 - optimization_system - WARNING - 某些目标函数可能使用替代方法
2025-07-30 15:17:17,570 - optimization_system - INFO - 加载训练好的分类器...
2025-07-30 15:17:18,721 - optimization_system - INFO - 开始执行基于分类学习的NSGA-III优化...
2025-07-30 15:17:18,722 - optimization_system - ERROR - 优化过程中发生错误: 未计算平均温度曲线
2025-07-30 15:17:18,735 - optimization_system - ERROR - Traceback (most recent call last):
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\run_classification_nsga3.py", line 350, in main
    results = optimizer.optimize()
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\classification_based_nsga3.py", line 312, in optimize
    results = self.nsga3_optimizer.optimize()
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\nsga3_optimizer.py", line 705, in optimize
    self.population = self._initialize_population()
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\nsga3_optimizer.py", line 176, in _initialize_population
    raise ValueError("未计算平均温度曲线")
ValueError: 未计算平均温度曲线

2025-07-30 15:17:47,445 - optimization_system - INFO - 开始基于分类学习的NSGA-III优化
2025-07-30 15:17:47,446 - optimization_system - INFO - 初始化基于分类学习的NSGA-III优化器...
2025-07-30 15:17:47,512 - optimization_system - INFO - 训练帕累托分类器...
2025-07-30 15:19:47,051 - optimization_system - INFO - 帕累托分类器训练完成
2025-07-30 15:19:47,051 - optimization_system - INFO -   优秀样本数量: 19
2025-07-30 15:19:47,051 - optimization_system - INFO -   较差样本数量: 2
2025-07-30 15:19:47,051 - optimization_system - INFO -   分类器准确率: 0.9100
2025-07-30 15:19:47,052 - optimization_system - ERROR - 优化过程中发生错误: name 'logger' is not defined
2025-07-30 15:19:47,052 - optimization_system - ERROR - Traceback (most recent call last):
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\run_classification_nsga3.py", line 321, in main
    if not validate_models(args.models_dir):
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\run_classification_nsga3.py", line 97, in validate_models
    logger.warning(f"缺少可选的模型文件: {missing_optional}")
NameError: name 'logger' is not defined

2025-07-30 15:26:39,678 - optimization_system - INFO - 开始基于分类学习的NSGA-III优化
2025-07-30 15:26:39,678 - optimization_system - INFO - 初始化基于分类学习的NSGA-III优化器...
2025-07-30 15:26:39,764 - optimization_system - INFO - 训练帕累托分类器...
2025-07-30 15:28:57,455 - optimization_system - INFO - 帕累托分类器训练完成
2025-07-30 15:28:57,455 - optimization_system - INFO -   优秀样本数量: 19
2025-07-30 15:28:57,455 - optimization_system - INFO -   较差样本数量: 2
2025-07-30 15:28:57,455 - optimization_system - INFO -   分类器准确率: 0.9100
2025-07-30 15:28:57,456 - optimization_system - WARNING - 缺少可选的模型文件: ['label1_classifier.joblib', 'label2_classifier.joblib']
2025-07-30 15:28:57,456 - optimization_system - WARNING - 某些目标函数可能使用替代方法
2025-07-30 15:28:57,456 - optimization_system - INFO - 加载训练好的分类器...
2025-07-30 15:28:57,527 - optimization_system - INFO - 开始执行基于分类学习的NSGA-III优化...
2025-07-30 15:28:57,528 - optimization_system - ERROR - 优化过程中发生错误: 未计算平均温度曲线
2025-07-30 15:28:57,530 - optimization_system - ERROR - Traceback (most recent call last):
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\run_classification_nsga3.py", line 355, in main
    logger.info("加载训练好的分类器...")
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\classification_based_nsga3.py", line 312, in optimize
    results = self.nsga3_optimizer.optimize()
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\nsga3_optimizer.py", line 705, in optimize
    self.population = self._initialize_population()
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\nsga3_optimizer.py", line 176, in _initialize_population
    raise ValueError("未计算平均温度曲线")
ValueError: 未计算平均温度曲线

2025-07-30 15:35:42,975 - optimization_system - INFO - 开始训练温度序列比较分类器
2025-07-30 15:35:42,977 - optimization_system - INFO - ==================================================
2025-07-30 15:35:42,977 - optimization_system - INFO - 步骤1: 加载和处理数据
2025-07-30 15:35:42,977 - optimization_system - INFO - ==================================================
2025-07-30 15:35:55,873 - optimization_system - INFO - 开始训练温度序列比较分类器
2025-07-30 15:35:55,874 - optimization_system - INFO - ==================================================
2025-07-30 15:35:55,874 - optimization_system - INFO - 步骤1: 加载和处理数据
2025-07-30 15:35:55,875 - optimization_system - INFO - ==================================================
2025-07-30 15:37:59,269 - optimization_system - INFO - 启用数据增强...
2025-07-30 15:37:59,655 - optimization_system - INFO - 数据集统计信息:
2025-07-30 15:37:59,656 - optimization_system - INFO -   sample_count: 21
2025-07-30 15:37:59,656 - optimization_system - INFO -   sequence_length: {'min': 18288, 'max': 50000, 'mean': 29522.619047619046, 'std': 9540.01208682568}
2025-07-30 15:37:59,656 - optimization_system - INFO -   temperature_range: {'min': 95.1, 'max': 150.9, 'mean': 134.65848542279932, 'std': 6.278435212711021}
2025-07-30 15:37:59,657 - optimization_system - INFO -   quality_scores: {'min': 0.0, 'max': 1.0, 'mean': 0.49659863945578225, 'std': 0.29283324149701884}
2025-07-30 15:37:59,657 - optimization_system - INFO -   pairwise_samples: 210
2025-07-30 15:37:59,657 - optimization_system - INFO - ==================================================
2025-07-30 15:37:59,657 - optimization_system - INFO - 步骤2: 特征提取
2025-07-30 15:37:59,657 - optimization_system - INFO - ==================================================
2025-07-30 15:38:06,278 - optimization_system - INFO - 特征提取完成:
2025-07-30 15:38:06,279 - optimization_system - INFO -   特征矩阵形状: (420, 407)
2025-07-30 15:38:06,279 - optimization_system - INFO -   特征数量: 407
2025-07-30 15:38:06,279 - optimization_system - INFO -   样本数量: 420
2025-07-30 15:38:06,279 - optimization_system - INFO -   标签分布: 类别0=270, 类别1=150
2025-07-30 15:38:06,279 - optimization_system - INFO - ==================================================
2025-07-30 15:38:06,280 - optimization_system - INFO - 步骤3: 训练分类器
2025-07-30 15:38:06,280 - optimization_system - INFO - ==================================================
2025-07-30 15:38:06,640 - optimization_system - INFO - 训练结果:
2025-07-30 15:38:06,640 - optimization_system - INFO -   训练准确率: 0.8750
2025-07-30 15:38:06,640 - optimization_system - INFO -   测试准确率: 0.8333
2025-07-30 15:38:06,641 - optimization_system - INFO -   交叉验证准确率: 0.8095 ± 0.0416
2025-07-30 15:38:06,641 - optimization_system - INFO -   测试AUC: 0.9068
2025-07-30 15:38:06,641 - optimization_system - INFO - ==================================================
2025-07-30 15:38:06,641 - optimization_system - INFO - 步骤3.5: 训练分类预测器和帕累托分析
2025-07-30 15:38:06,641 - optimization_system - INFO - ==================================================
2025-07-30 15:38:06,641 - optimization_system - INFO - 训练基于分类学习的标签预测器...
2025-07-30 15:40:05,454 - optimization_system - INFO - 帕累托分类器训练结果:
2025-07-30 15:40:05,454 - optimization_system - INFO -   分类器准确率: 0.9100 ± 0.1114
2025-07-30 15:40:05,454 - optimization_system - INFO -   特征数量: 101
2025-07-30 15:40:05,454 - optimization_system - INFO -   训练样本数量: 21
2025-07-30 15:40:05,454 - optimization_system - INFO -   优秀样本数量: 19
2025-07-30 15:40:05,455 - optimization_system - INFO -   较差样本数量: 2
2025-07-30 15:40:05,455 - optimization_system - INFO - ==================================================
2025-07-30 15:40:05,456 - optimization_system - INFO - 步骤4: 保存模型
2025-07-30 15:40:05,456 - optimization_system - INFO - ==================================================
2025-07-30 15:40:05,549 - optimization_system - INFO - 特征提取器已保存到 models\feature_extractor.joblib
2025-07-30 15:40:05,556 - optimization_system - INFO - 分类预测器已保存到 models\classification_predictor.joblib
2025-07-30 15:40:05,556 - optimization_system - INFO - 帕累托分类器已自动保存到models目录
2025-07-30 15:40:05,556 - optimization_system - INFO -   - pareto_sequence_classifier.joblib: 帕累托分类器
2025-07-30 15:40:05,557 - optimization_system - INFO -   - pareto_sequence_scaler.joblib: 特征标准化器
2025-07-30 15:40:05,557 - optimization_system - INFO -   - pareto_classification.joblib: 样本分类结果
2025-07-30 15:40:05,558 - optimization_system - INFO - 训练摘要已保存到 models\training_summary_20250730_154005.txt
2025-07-30 15:40:05,558 - optimization_system - INFO - ==================================================
2025-07-30 15:40:05,559 - optimization_system - INFO - 步骤6: 模型验证
2025-07-30 15:40:05,559 - optimization_system - INFO - ==================================================
2025-07-30 15:40:05,630 - optimization_system - INFO - 验证结果:
2025-07-30 15:40:05,630 - optimization_system - INFO -   准确率: 0.8667
2025-07-30 15:40:05,630 - optimization_system - INFO -   AUC: 0.9507
2025-07-30 15:40:05,631 - optimization_system - INFO - ==================================================
2025-07-30 15:40:05,631 - optimization_system - INFO - 分类器训练完成！
2025-07-30 15:40:05,631 - optimization_system - INFO - ==================================================
2025-07-30 15:40:05,631 - optimization_system - INFO - 模型文件保存在: models
2025-07-30 15:40:05,631 - optimization_system - INFO - 训练摘要保存在: models\training_summary_20250730_154005.txt
2025-07-30 15:40:47,359 - optimization_system - INFO - 开始基于分类学习的NSGA-III优化
2025-07-30 15:40:47,360 - optimization_system - INFO - 初始化基于分类学习的NSGA-III优化器...
2025-07-30 15:40:47,438 - optimization_system - INFO - 跳过帕累托分类器训练，将直接加载已训练的模型
2025-07-30 15:40:47,439 - optimization_system - WARNING - 缺少标签分类器文件: ['label1_classifier.joblib', 'label2_classifier.joblib']
2025-07-30 15:40:47,440 - optimization_system - WARNING - 对应的目标函数将使用简单统计特征替代
2025-07-30 15:40:47,440 - optimization_system - INFO - 加载训练好的分类器...
2025-07-30 15:40:48,511 - optimization_system - INFO - 开始执行基于分类学习的NSGA-III优化...
2025-07-30 15:40:48,512 - optimization_system - ERROR - 优化过程中发生错误: 未计算平均温度曲线
2025-07-30 15:40:48,514 - optimization_system - ERROR - Traceback (most recent call last):
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\run_classification_nsga3.py", line 381, in main
    results = optimizer.optimize()
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\classification_based_nsga3.py", line 312, in optimize
    results = self.nsga3_optimizer.optimize()
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\nsga3_optimizer.py", line 705, in optimize
    """
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\nsga3_optimizer.py", line 176, in _initialize_population
    self._initialize_components()
ValueError: 未计算平均温度曲线

2025-07-30 15:48:56,990 - optimization_system - INFO - 开始训练基于帕累托支配关系的温度序列分类器
2025-07-30 15:48:56,991 - optimization_system - INFO - ==================================================
2025-07-30 15:48:56,991 - optimization_system - INFO - 步骤1: 初始化帕累托分类器训练器
2025-07-30 15:48:56,991 - optimization_system - INFO - ==================================================
2025-07-30 15:48:57,202 - optimization_system - INFO - ==================================================
2025-07-30 15:48:57,203 - optimization_system - INFO - 步骤2: 加载和分析数据
2025-07-30 15:48:57,203 - optimization_system - INFO - ==================================================
2025-07-30 15:50:51,457 - optimization_system - INFO - 数据分析完成:
2025-07-30 15:50:51,457 - optimization_system - INFO -   样本数量: 21
2025-07-30 15:50:51,457 - optimization_system - INFO -   优秀样本数量: 19
2025-07-30 15:50:51,457 - optimization_system - INFO -   较差样本数量: 2
2025-07-30 15:50:51,458 - optimization_system - INFO -   帕累托前沿大小: 7
2025-07-30 15:50:51,458 - optimization_system - INFO - ==================================================
2025-07-30 15:50:51,458 - optimization_system - INFO - 步骤3: 训练帕累托分类器
2025-07-30 15:50:51,458 - optimization_system - INFO - ==================================================
2025-07-30 15:50:53,316 - optimization_system - INFO - 分类器训练完成:
2025-07-30 15:50:53,317 - optimization_system - INFO -   交叉验证准确率: 0.9100 ± 0.1114
2025-07-30 15:50:53,318 - optimization_system - INFO -   特征数量: 101
2025-07-30 15:50:53,319 - optimization_system - INFO -   训练样本数量: 21
2025-07-30 15:50:53,320 - optimization_system - INFO - ==================================================
2025-07-30 15:50:53,322 - optimization_system - INFO - 步骤4: 保存分类器
2025-07-30 15:50:53,322 - optimization_system - INFO - ==================================================
2025-07-30 15:50:53,384 - optimization_system - INFO - ==================================================
2025-07-30 15:50:53,384 - optimization_system - INFO - 步骤5: 保存分析结果
2025-07-30 15:50:53,384 - optimization_system - INFO - ==================================================
2025-07-30 15:50:53,448 - optimization_system - ERROR - 训练过程中发生错误: name 'logger' is not defined
2025-07-30 15:50:53,449 - optimization_system - ERROR - Traceback (most recent call last):
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\train_pareto_classifier.py", line 194, in main
    save_analysis_results(analysis_results, training_results, args.output_dir, timestamp)
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\train_pareto_classifier.py", line 80, in save_analysis_results
    logger.info(f"样本分类结果已保存到 {classification_path}")
NameError: name 'logger' is not defined

2025-07-30 15:56:36,854 - optimization_system - INFO - 开始训练基于帕累托支配关系的温度序列分类器
2025-07-30 15:56:36,855 - optimization_system - INFO - ==================================================
2025-07-30 15:56:36,855 - optimization_system - INFO - 步骤1: 初始化帕累托分类器训练器
2025-07-30 15:56:36,856 - optimization_system - INFO - ==================================================
2025-07-30 15:56:37,047 - optimization_system - INFO - ==================================================
2025-07-30 15:56:37,047 - optimization_system - INFO - 步骤2: 加载和分析数据
2025-07-30 15:56:37,047 - optimization_system - INFO - ==================================================
2025-07-30 15:58:39,146 - optimization_system - INFO - 数据分析完成:
2025-07-30 15:58:39,146 - optimization_system - INFO -   样本数量: 21
2025-07-30 15:58:39,146 - optimization_system - INFO -   优秀样本数量: 19
2025-07-30 15:58:39,146 - optimization_system - INFO -   较差样本数量: 2
2025-07-30 15:58:39,146 - optimization_system - INFO -   帕累托前沿大小: 7
2025-07-30 15:58:39,146 - optimization_system - INFO - ==================================================
2025-07-30 15:58:39,147 - optimization_system - INFO - 步骤3: 训练帕累托分类器
2025-07-30 15:58:39,147 - optimization_system - INFO - ==================================================
2025-07-30 15:58:40,202 - optimization_system - INFO - 分类器训练完成:
2025-07-30 15:58:40,202 - optimization_system - INFO -   交叉验证准确率: 0.9100 ± 0.1114
2025-07-30 15:58:40,203 - optimization_system - INFO -   特征数量: 101
2025-07-30 15:58:40,203 - optimization_system - INFO -   训练样本数量: 21
2025-07-30 15:58:40,203 - optimization_system - INFO - ==================================================
2025-07-30 15:58:40,203 - optimization_system - INFO - 步骤4: 保存分类器
2025-07-30 15:58:40,203 - optimization_system - INFO - ==================================================
2025-07-30 15:58:40,280 - optimization_system - INFO - ==================================================
2025-07-30 15:58:40,281 - optimization_system - INFO - 步骤5: 保存分析结果
2025-07-30 15:58:40,281 - optimization_system - INFO - ==================================================
2025-07-30 15:58:40,288 - optimization_system - INFO - 样本分类结果已保存到 models\pareto_sample_classification_20250730_155840.csv
2025-07-30 15:58:40,356 - optimization_system - INFO - 目标函数矩阵已保存到 models\objectives_matrix_20250730_155840.csv
2025-07-30 15:58:40,356 - optimization_system - INFO - 训练摘要已保存到 models\pareto_training_summary_20250730_155840.txt
2025-07-30 15:58:40,357 - optimization_system - INFO - ==================================================
2025-07-30 15:58:40,357 - optimization_system - INFO - 步骤6: 验证分类器
2025-07-30 15:58:40,357 - optimization_system - INFO - ==================================================
2025-07-30 15:58:40,375 - optimization_system - INFO - 测试样本 1 的预测结果:
2025-07-30 15:58:40,375 - optimization_system - INFO -   预测类别: 优秀序列
2025-07-30 15:58:40,375 - optimization_system - INFO -   置信度: 0.9200
2025-07-30 15:58:40,375 - optimization_system - INFO -   优秀概率: 0.9200
2025-07-30 15:58:40,376 - optimization_system - INFO -   较差概率: 0.0800
2025-07-30 15:58:40,376 - optimization_system - INFO - ==================================================
2025-07-30 15:58:40,376 - optimization_system - INFO - 帕累托分类器训练完成！
2025-07-30 15:58:40,377 - optimization_system - INFO - ==================================================
2025-07-30 15:58:40,377 - optimization_system - INFO - 模型文件保存在: models
2025-07-30 15:58:40,377 - optimization_system - INFO - 可以使用以下文件:
2025-07-30 15:58:40,377 - optimization_system - INFO -   - pareto_sequence_classifier.joblib: 帕累托分类器
2025-07-30 15:58:40,378 - optimization_system - INFO -   - pareto_sequence_scaler.joblib: 特征标准化器
2025-07-30 15:58:40,378 - optimization_system - INFO -   - pareto_classification.joblib: 样本分类结果
2025-07-30 16:07:38,696 - optimization_system - INFO - 开始基于分类学习的NSGA-III优化
2025-07-30 16:07:38,697 - optimization_system - INFO - 初始化基于分类学习的NSGA-III优化器...
2025-07-30 16:07:38,788 - optimization_system - INFO - 跳过帕累托分类器训练，将直接加载已训练的模型
2025-07-30 16:07:38,789 - optimization_system - ERROR - 缺少必要的模型文件: ['feature_extractor.joblib']
2025-07-30 16:07:38,789 - optimization_system - ERROR - 模型文件验证失败，请先训练相关分类器
2025-07-30 16:10:51,792 - optimization_system - INFO - 开始基于分类学习的NSGA-III优化
2025-07-30 16:10:51,793 - optimization_system - INFO - 初始化基于分类学习的NSGA-III优化器...
2025-07-30 16:10:51,854 - optimization_system - INFO - 跳过帕累托分类器训练，将直接加载已训练的模型
2025-07-30 16:10:51,854 - optimization_system - ERROR - 缺少必要的模型文件: ['feature_extractor.joblib']
2025-07-30 16:10:51,855 - optimization_system - ERROR - 模型文件验证失败，请先训练相关分类器
2025-07-30 16:20:44,178 - optimization_system - INFO - 开始训练基于帕累托支配关系的温度序列分类器
2025-07-30 16:20:44,179 - optimization_system - INFO - ==================================================
2025-07-30 16:20:44,180 - optimization_system - INFO - 步骤1: 初始化帕累托分类器训练器
2025-07-30 16:20:44,180 - optimization_system - INFO - ==================================================
2025-07-30 16:20:44,492 - optimization_system - INFO - ==================================================
2025-07-30 16:20:44,492 - optimization_system - INFO - 步骤2: 加载和分析数据
2025-07-30 16:20:44,492 - optimization_system - INFO - ==================================================
2025-07-30 16:22:38,982 - optimization_system - INFO - 数据分析完成:
2025-07-30 16:22:38,982 - optimization_system - INFO -   样本数量: 21
2025-07-30 16:22:38,983 - optimization_system - INFO -   优秀样本数量: 19
2025-07-30 16:22:38,983 - optimization_system - INFO -   较差样本数量: 2
2025-07-30 16:22:38,983 - optimization_system - INFO -   帕累托前沿大小: 7
2025-07-30 16:22:38,983 - optimization_system - INFO - ==================================================
2025-07-30 16:22:38,984 - optimization_system - INFO - 步骤3: 训练帕累托分类器
2025-07-30 16:22:38,984 - optimization_system - INFO - ==================================================
2025-07-30 16:22:40,386 - optimization_system - INFO - 分类器训练完成:
2025-07-30 16:22:40,386 - optimization_system - INFO -   交叉验证准确率: 0.9100 ± 0.1114
2025-07-30 16:22:40,387 - optimization_system - INFO -   特征数量: 101
2025-07-30 16:22:40,387 - optimization_system - INFO -   训练样本数量: 21
2025-07-30 16:22:40,387 - optimization_system - INFO - ==================================================
2025-07-30 16:22:40,388 - optimization_system - INFO - 步骤4: 保存分类器
2025-07-30 16:22:40,388 - optimization_system - INFO - ==================================================
2025-07-30 16:22:40,452 - optimization_system - INFO - ==================================================
2025-07-30 16:22:40,452 - optimization_system - INFO - 步骤5: 保存分析结果
2025-07-30 16:22:40,452 - optimization_system - INFO - ==================================================
2025-07-30 16:22:40,554 - optimization_system - INFO - 样本分类结果已保存到 models\pareto_sample_classification_20250730_162240.csv
2025-07-30 16:22:40,593 - optimization_system - INFO - 目标函数矩阵已保存到 models\objectives_matrix_20250730_162240.csv
2025-07-30 16:22:40,606 - optimization_system - INFO - 训练摘要已保存到 models\pareto_training_summary_20250730_162240.txt
2025-07-30 16:22:40,607 - optimization_system - INFO - ==================================================
2025-07-30 16:22:40,607 - optimization_system - INFO - 步骤6: 验证分类器
2025-07-30 16:22:40,607 - optimization_system - INFO - ==================================================
2025-07-30 16:22:40,642 - optimization_system - INFO - 测试样本 1 的预测结果:
2025-07-30 16:22:40,643 - optimization_system - INFO -   预测类别: 优秀序列
2025-07-30 16:22:40,643 - optimization_system - INFO -   置信度: 0.9000
2025-07-30 16:22:40,644 - optimization_system - INFO -   优秀概率: 0.9000
2025-07-30 16:22:40,644 - optimization_system - INFO -   较差概率: 0.1000
2025-07-30 16:22:40,645 - optimization_system - INFO - ==================================================
2025-07-30 16:22:40,645 - optimization_system - INFO - 帕累托分类器训练完成！
2025-07-30 16:22:40,645 - optimization_system - INFO - ==================================================
2025-07-30 16:22:40,645 - optimization_system - INFO - 模型文件保存在: models
2025-07-30 16:22:40,645 - optimization_system - INFO - 可以使用以下文件:
2025-07-30 16:22:40,646 - optimization_system - INFO -   - pareto_sequence_classifier.joblib: 帕累托分类器
2025-07-30 16:22:40,646 - optimization_system - INFO -   - pareto_sequence_scaler.joblib: 特征标准化器
2025-07-30 16:22:40,646 - optimization_system - INFO -   - pareto_classification.joblib: 样本分类结果
2025-07-30 16:23:10,895 - optimization_system - INFO - 开始基于分类学习的NSGA-III优化
2025-07-30 16:23:10,896 - optimization_system - INFO - 初始化基于分类学习的NSGA-III优化器...
2025-07-30 16:23:10,975 - optimization_system - INFO - 跳过帕累托分类器训练，将直接加载已训练的模型
2025-07-30 16:23:10,976 - optimization_system - INFO - 缺少标签分类器文件: ['label1_classifier.joblib', 'label2_classifier.joblib']
2025-07-30 16:23:10,977 - optimization_system - INFO - 将使用简单统计特征作为目标函数替代
2025-07-30 16:23:10,977 - optimization_system - INFO - 加载训练好的分类器...
2025-07-30 16:23:11,627 - optimization_system - INFO - 开始执行基于分类学习的NSGA-III优化...
2025-07-30 16:23:11,628 - optimization_system - ERROR - 优化过程中发生错误: 未计算平均温度曲线
2025-07-30 16:23:11,630 - optimization_system - ERROR - Traceback (most recent call last):
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\run_classification_nsga3.py", line 365, in main
    results = optimizer.optimize()
              ^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\classification_based_nsga3.py", line 317, in optimize
    results = self.nsga3_optimizer.optimize()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\nsga3_optimizer.py", line 859, in optimize
    self.population = self._initialize_population()
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\nsga3_optimizer.py", line 205, in _initialize_population
    raise ValueError("未计算平均温度曲线")
ValueError: 未计算平均温度曲线

2025-07-30 16:24:40,047 - optimization_system - INFO - 开始基于分类学习的NSGA-III优化
2025-07-30 16:24:40,047 - optimization_system - INFO - 初始化基于分类学习的NSGA-III优化器...
2025-07-30 16:24:40,118 - optimization_system - INFO - 跳过帕累托分类器训练，将直接加载已训练的模型
2025-07-30 16:24:40,120 - optimization_system - INFO - 缺少标签分类器文件: ['label1_classifier.joblib', 'label2_classifier.joblib']
2025-07-30 16:24:40,120 - optimization_system - INFO - 将使用简单统计特征作为目标函数替代
2025-07-30 16:24:40,120 - optimization_system - INFO - 加载训练好的分类器...
2025-07-30 16:24:40,743 - optimization_system - INFO - 开始执行基于分类学习的NSGA-III优化...
2025-07-30 16:24:40,758 - optimization_system - ERROR - 优化过程中发生错误: 未计算平均温度曲线
2025-07-30 16:24:40,759 - optimization_system - ERROR - Traceback (most recent call last):
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\run_classification_nsga3.py", line 365, in main
    results = optimizer.optimize()
              ^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\classification_based_nsga3.py", line 317, in optimize
    results = self.nsga3_optimizer.optimize()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\nsga3_optimizer.py", line 861, in optimize
    self.population = self._initialize_population()
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\nsga3_optimizer.py", line 206, in _initialize_population
    raise ValueError("未计算平均温度曲线")
ValueError: 未计算平均温度曲线

2025-07-30 16:27:18,280 - optimization_system - INFO - 开始基于分类学习的NSGA-III优化
2025-07-30 16:27:18,280 - optimization_system - INFO - 初始化基于分类学习的NSGA-III优化器...
2025-07-30 16:27:18,366 - optimization_system - INFO - 跳过帕累托分类器训练，将直接加载已训练的模型
2025-07-30 16:27:18,366 - optimization_system - INFO - 缺少标签分类器文件: ['label1_classifier.joblib', 'label2_classifier.joblib']
2025-07-30 16:27:18,367 - optimization_system - INFO - 将使用简单统计特征作为目标函数替代
2025-07-30 16:27:18,367 - optimization_system - INFO - 加载训练好的分类器...
2025-07-30 16:27:18,998 - optimization_system - INFO - 开始执行基于分类学习的NSGA-III优化...
2025-07-30 16:37:26,619 - optimization_system - INFO - 基于分类学习的NSGA-III优化完成
2025-07-30 16:37:26,620 - optimization_system - INFO - 总优化时间: 607.62秒
2025-07-30 16:37:26,620 - optimization_system - INFO - Pareto前沿解数量: 50
2025-07-30 16:37:26,620 - optimization_system - INFO - Pareto前沿大小: 50
2025-07-30 16:37:26,728 - optimization_system - INFO - 优化结果已保存到 results\classification_nsga3_results_20250730_163726.json
2025-07-30 16:37:26,749 - optimization_system - INFO - Pareto前沿已保存到 results\classification_nsga3_pareto_front_20250730_163726.csv
2025-07-30 16:37:26,843 - optimization_system - INFO - 最优解已保存到 results\classification_nsga3_best_solutions_20250730_163726.json
2025-07-30 16:37:32,228 - optimization_system - INFO - 结果图表已保存到 results\classification_nsga3_results_20250730_163726.png
2025-07-30 16:38:24,543 - optimization_system - INFO - ==================================================
2025-07-30 16:38:24,543 - optimization_system - INFO - 基于分类学习的NSGA-III优化完成！
2025-07-30 16:38:24,544 - optimization_system - INFO - ==================================================
2025-07-30 16:38:24,544 - optimization_system - INFO - 结果文件保存在: results
2025-07-30 16:52:24,872 - optimization_system - INFO - 开始训练基于帕累托支配关系的温度序列分类器
2025-07-30 16:52:24,872 - optimization_system - INFO - ==================================================
2025-07-30 16:52:24,873 - optimization_system - INFO - 步骤1: 初始化帕累托分类器训练器
2025-07-30 16:52:24,873 - optimization_system - INFO - ==================================================
2025-07-30 16:52:25,058 - optimization_system - INFO - ==================================================
2025-07-30 16:52:25,059 - optimization_system - INFO - 步骤2: 加载和分析数据
2025-07-30 16:52:25,060 - optimization_system - INFO - ==================================================
2025-07-30 16:54:29,587 - optimization_system - INFO - 数据分析完成:
2025-07-30 16:54:29,587 - optimization_system - INFO -   样本数量: 21
2025-07-30 16:54:29,588 - optimization_system - INFO -   优秀样本数量: 10
2025-07-30 16:54:29,588 - optimization_system - INFO -   较差样本数量: 11
2025-07-30 16:54:29,588 - optimization_system - ERROR - 训练过程中发生错误: 'pareto_front_indices'
2025-07-30 16:54:29,589 - optimization_system - ERROR - Traceback (most recent call last):
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\train_pareto_classifier.py", line 176, in main
    logger.info(f"  帕累托前沿大小: {len(analysis_results['pareto_results']['pareto_front_indices'])}")
                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
KeyError: 'pareto_front_indices'

2025-07-30 16:56:12,598 - optimization_system - INFO - 开始训练基于帕累托支配关系的温度序列分类器
2025-07-30 16:56:12,598 - optimization_system - INFO - ==================================================
2025-07-30 16:56:12,599 - optimization_system - INFO - 步骤1: 初始化帕累托分类器训练器
2025-07-30 16:56:12,599 - optimization_system - INFO - ==================================================
2025-07-30 16:56:12,758 - optimization_system - INFO - ==================================================
2025-07-30 16:56:12,759 - optimization_system - INFO - 步骤2: 加载和分析数据
2025-07-30 16:56:12,760 - optimization_system - INFO - ==================================================
2025-07-30 16:58:16,067 - optimization_system - INFO - 数据分析完成:
2025-07-30 16:58:16,068 - optimization_system - INFO -   样本数量: 21
2025-07-30 16:58:16,068 - optimization_system - INFO -   优秀样本数量: 10
2025-07-30 16:58:16,068 - optimization_system - INFO -   较差样本数量: 11
2025-07-30 16:58:16,069 - optimization_system - INFO -   分类方法: comprehensive_ranking
2025-07-30 16:58:16,069 - optimization_system - INFO - ==================================================
2025-07-30 16:58:16,069 - optimization_system - INFO - 步骤3: 训练帕累托分类器
2025-07-30 16:58:16,069 - optimization_system - INFO - ==================================================
2025-07-30 16:58:17,426 - optimization_system - INFO - 分类器训练完成:
2025-07-30 16:58:17,426 - optimization_system - INFO -   交叉验证准确率: 0.5600 ± 0.1985
2025-07-30 16:58:17,426 - optimization_system - INFO -   特征数量: 101
2025-07-30 16:58:17,426 - optimization_system - INFO -   训练样本数量: 21
2025-07-30 16:58:17,427 - optimization_system - INFO - ==================================================
2025-07-30 16:58:17,427 - optimization_system - INFO - 步骤4: 保存分类器
2025-07-30 16:58:17,427 - optimization_system - INFO - ==================================================
2025-07-30 16:58:17,486 - optimization_system - INFO - ==================================================
2025-07-30 16:58:17,487 - optimization_system - INFO - 步骤5: 保存分析结果
2025-07-30 16:58:17,487 - optimization_system - INFO - ==================================================
2025-07-30 16:58:17,566 - optimization_system - INFO - 样本分类结果已保存到 models\pareto_sample_classification_20250730_165817.csv
2025-07-30 16:58:17,601 - optimization_system - INFO - 目标函数矩阵已保存到 models\objectives_matrix_20250730_165817.csv
2025-07-30 16:58:17,602 - optimization_system - INFO - 训练摘要已保存到 models\pareto_training_summary_20250730_165817.txt
2025-07-30 16:58:17,603 - optimization_system - INFO - ==================================================
2025-07-30 16:58:17,603 - optimization_system - INFO - 步骤6: 验证分类器
2025-07-30 16:58:17,603 - optimization_system - INFO - ==================================================
2025-07-30 16:58:17,636 - optimization_system - INFO - 测试样本 1 的预测结果:
2025-07-30 16:58:17,636 - optimization_system - INFO -   预测类别: 优秀序列
2025-07-30 16:58:17,636 - optimization_system - INFO -   置信度: 0.7700
2025-07-30 16:58:17,637 - optimization_system - INFO -   优秀概率: 0.7700
2025-07-30 16:58:17,637 - optimization_system - INFO -   较差概率: 0.2300
2025-07-30 16:58:17,638 - optimization_system - INFO - ==================================================
2025-07-30 16:58:17,638 - optimization_system - INFO - 帕累托分类器训练完成！
2025-07-30 16:58:17,638 - optimization_system - INFO - ==================================================
2025-07-30 16:58:17,638 - optimization_system - INFO - 模型文件保存在: models
2025-07-30 16:58:17,639 - optimization_system - INFO - 可以使用以下文件:
2025-07-30 16:58:17,639 - optimization_system - INFO -   - pareto_sequence_classifier.joblib: 帕累托分类器
2025-07-30 16:58:17,639 - optimization_system - INFO -   - pareto_sequence_scaler.joblib: 特征标准化器
2025-07-30 16:58:17,639 - optimization_system - INFO -   - pareto_classification.joblib: 样本分类结果
2025-07-30 16:59:09,877 - optimization_system - INFO - 开始基于分类学习的NSGA-III优化
2025-07-30 16:59:09,878 - optimization_system - INFO - 初始化基于分类学习的NSGA-III优化器...
2025-07-30 16:59:09,960 - optimization_system - INFO - 跳过帕累托分类器训练，将直接加载已训练的模型
2025-07-30 16:59:09,961 - optimization_system - INFO - 加载训练好的分类器...
2025-07-30 16:59:10,618 - optimization_system - INFO - 开始执行基于分类学习的NSGA-III优化...
2025-07-30 17:08:27,973 - optimization_system - INFO - 基于分类学习的NSGA-III优化完成
2025-07-30 17:08:27,973 - optimization_system - INFO - 总优化时间: 557.35秒
2025-07-30 17:08:27,973 - optimization_system - INFO - Pareto前沿解数量: 50
2025-07-30 17:08:27,974 - optimization_system - INFO - Pareto前沿大小: 50
2025-07-30 17:08:28,062 - optimization_system - INFO - 优化结果已保存到 results\classification_nsga3_results_20250730_170827.json
2025-07-30 17:08:28,072 - optimization_system - INFO - Pareto前沿已保存到 results\classification_nsga3_pareto_front_20250730_170827.csv
2025-07-30 17:08:28,150 - optimization_system - INFO - 最优解已保存到 results\classification_nsga3_best_solutions_20250730_170827.json
2025-07-30 17:08:32,918 - optimization_system - INFO - 结果图表已保存到 results\classification_nsga3_results_20250730_170827.png
2025-07-30 17:08:53,537 - optimization_system - INFO - ==================================================
2025-07-30 17:08:53,537 - optimization_system - INFO - 基于分类学习的NSGA-III优化完成！
2025-07-30 17:08:53,537 - optimization_system - INFO - ==================================================
2025-07-30 17:08:53,538 - optimization_system - INFO - 结果文件保存在: results
2025-07-30 17:14:04,122 - optimization_system - INFO - 开始基于分类学习的NSGA-III优化
2025-07-30 17:14:04,123 - optimization_system - INFO - 初始化基于分类学习的NSGA-III优化器...
2025-07-30 17:14:04,221 - optimization_system - INFO - 跳过帕累托分类器训练，将直接加载已训练的模型
2025-07-30 17:14:04,222 - optimization_system - INFO - 加载训练好的分类器...
2025-07-30 17:14:04,795 - optimization_system - INFO - 开始执行基于分类学习的NSGA-III优化...
2025-07-30 17:15:28,486 - optimization_system - ERROR - 优化过程中发生错误: 'BusinessDataAnalyzer' object has no attribute 'calculate_constraint_bounds'
2025-07-30 17:15:28,488 - optimization_system - ERROR - Traceback (most recent call last):
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\run_classification_nsga3.py", line 363, in main
    results = optimizer.optimize()
              ^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\classification_based_nsga3.py", line 317, in optimize
    results = self.nsga3_optimizer.optimize()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\nsga3_optimizer.py", line 942, in optimize
    self.population = self._initialize_population()
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\nsga3_optimizer.py", line 201, in _initialize_population
    self._initialize_components()
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\nsga3_optimizer.py", line 364, in _initialize_components
    self.lower_bound_curve, self.upper_bound_curve = self.business_analyzer.calculate_constraint_bounds()
                                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'BusinessDataAnalyzer' object has no attribute 'calculate_constraint_bounds'

2025-07-30 17:16:49,617 - optimization_system - INFO - 开始基于分类学习的NSGA-III优化
2025-07-30 17:16:49,618 - optimization_system - INFO - 初始化基于分类学习的NSGA-III优化器...
2025-07-30 17:16:49,670 - optimization_system - INFO - 跳过帕累托分类器训练，将直接加载已训练的模型
2025-07-30 17:16:49,671 - optimization_system - INFO - 加载训练好的分类器...
2025-07-30 17:16:50,202 - optimization_system - INFO - 开始执行基于分类学习的NSGA-III优化...
2025-07-30 17:38:50,498 - optimization_system - INFO - 开始基于分类学习的NSGA-III优化
2025-07-30 17:38:50,498 - optimization_system - INFO - 初始化基于分类学习的NSGA-III优化器...
2025-07-30 17:38:50,553 - optimization_system - INFO - 跳过帕累托分类器训练，将直接加载已训练的模型
2025-07-30 17:38:50,554 - optimization_system - INFO - 加载训练好的分类器...
2025-07-30 17:38:51,097 - optimization_system - INFO - 开始执行基于分类学习的NSGA-III优化...
2025-07-30 18:12:51,648 - optimization_system - INFO - 开始基于分类学习的NSGA-III优化
2025-07-30 18:12:51,649 - optimization_system - INFO - 初始化基于分类学习的NSGA-III优化器...
2025-07-30 18:12:51,704 - optimization_system - INFO - 跳过帕累托分类器训练，将直接加载已训练的模型
2025-07-30 18:12:51,705 - optimization_system - INFO - 加载训练好的分类器...
2025-07-30 18:12:52,262 - optimization_system - INFO - 开始执行基于分类学习的NSGA-III优化...
2025-07-30 18:58:37,982 - optimization_system - INFO - 开始基于分类学习的NSGA-III优化
2025-07-30 18:58:37,982 - optimization_system - INFO - 初始化基于分类学习的NSGA-III优化器...
2025-07-30 18:58:38,037 - optimization_system - INFO - 跳过帕累托分类器训练，将直接加载已训练的模型
2025-07-30 18:58:38,039 - optimization_system - INFO - 加载训练好的分类器...
2025-07-30 18:58:40,339 - optimization_system - INFO - 开始执行基于分类学习的NSGA-III优化...
2025-07-30 19:00:12,634 - optimization_system - ERROR - 优化过程中发生错误: 'DecisionTreeClassifier' object has no attribute 'monotonic_cst'
2025-07-30 19:00:12,657 - optimization_system - ERROR - Traceback (most recent call last):
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\run_classification_nsga3.py", line 363, in main
    results = optimizer.optimize()
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\classification_based_nsga3.py", line 317, in optimize
    results = self.nsga3_optimizer.optimize()
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\nsga3_optimizer.py", line 973, in optimize
    self.population = self._environmental_selection(combined_population)
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\nsga3_optimizer.py", line 673, in _environmental_selection
    return self._classification_based_selection(population)
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\nsga3_optimizer.py", line 690, in _classification_based_selection
    fronts = self._non_dominated_sorting(population)
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\nsga3_optimizer.py", line 500, in _non_dominated_sorting
    return self._classification_based_sorting(population)
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\nsga3_optimizer.py", line 526, in _classification_based_sorting
    prediction = self.pareto_classifier.predict(features_scaled)[0]
  File "D:\anaconda3\envs\chemo\lib\site-packages\sklearn\ensemble\_forest.py", line 904, in predict
    proba = self.predict_proba(X)
  File "D:\anaconda3\envs\chemo\lib\site-packages\sklearn\ensemble\_forest.py", line 946, in predict_proba
    X = self._validate_X_predict(X)
  File "D:\anaconda3\envs\chemo\lib\site-packages\sklearn\ensemble\_forest.py", line 633, in _validate_X_predict
    if self.estimators_[0]._support_missing_values(X):
  File "D:\anaconda3\envs\chemo\lib\site-packages\sklearn\tree\_classes.py", line 188, in _support_missing_values
    and self.monotonic_cst is None
AttributeError: 'DecisionTreeClassifier' object has no attribute 'monotonic_cst'

2025-07-30 19:28:41,281 - optimization_system - INFO - 开始训练基于帕累托支配关系的温度序列分类器
2025-07-30 19:28:41,281 - optimization_system - INFO - ==================================================
2025-07-30 19:28:41,282 - optimization_system - INFO - 步骤1: 初始化帕累托分类器训练器
2025-07-30 19:28:41,282 - optimization_system - INFO - ==================================================
2025-07-30 19:28:41,665 - optimization_system - INFO - ==================================================
2025-07-30 19:28:41,666 - optimization_system - INFO - 步骤2: 加载和分析数据
2025-07-30 19:28:41,667 - optimization_system - INFO - ==================================================
2025-07-30 19:30:38,372 - optimization_system - INFO - 数据分析完成:
2025-07-30 19:30:38,373 - optimization_system - INFO -   样本数量: 21
2025-07-30 19:30:38,373 - optimization_system - INFO -   优秀样本数量: 10
2025-07-30 19:30:38,373 - optimization_system - INFO -   较差样本数量: 11
2025-07-30 19:30:38,374 - optimization_system - INFO -   分类方法: comprehensive_ranking
2025-07-30 19:30:38,375 - optimization_system - INFO - ==================================================
2025-07-30 19:30:38,375 - optimization_system - INFO - 步骤3: 训练帕累托分类器
2025-07-30 19:30:38,375 - optimization_system - INFO - ==================================================
2025-07-30 19:30:40,187 - optimization_system - INFO - 分类器训练完成:
2025-07-30 19:30:40,187 - optimization_system - INFO -   交叉验证准确率: 0.5100 ± 0.2354
2025-07-30 19:30:40,187 - optimization_system - INFO -   特征数量: 101
2025-07-30 19:30:40,187 - optimization_system - INFO -   训练样本数量: 21
2025-07-30 19:30:40,188 - optimization_system - INFO - ==================================================
2025-07-30 19:30:40,188 - optimization_system - INFO - 步骤4: 保存分类器
2025-07-30 19:30:40,188 - optimization_system - INFO - ==================================================
2025-07-30 19:30:40,230 - optimization_system - INFO - ==================================================
2025-07-30 19:30:40,230 - optimization_system - INFO - 步骤5: 保存分析结果
2025-07-30 19:30:40,231 - optimization_system - INFO - ==================================================
2025-07-30 19:30:40,318 - optimization_system - INFO - 样本分类结果已保存到 models\pareto_sample_classification_20250730_193040.csv
2025-07-30 19:30:40,356 - optimization_system - INFO - 目标函数矩阵已保存到 models\objectives_matrix_20250730_193040.csv
2025-07-30 19:30:40,357 - optimization_system - INFO - 训练摘要已保存到 models\pareto_training_summary_20250730_193040.txt
2025-07-30 19:30:40,357 - optimization_system - INFO - ==================================================
2025-07-30 19:30:40,358 - optimization_system - INFO - 步骤6: 验证分类器
2025-07-30 19:30:40,358 - optimization_system - INFO - ==================================================
2025-07-30 19:30:40,381 - optimization_system - INFO - 测试样本 1 的预测结果:
2025-07-30 19:30:40,381 - optimization_system - INFO -   预测类别: 优秀序列
2025-07-30 19:30:40,382 - optimization_system - INFO -   置信度: 0.7700
2025-07-30 19:30:40,382 - optimization_system - INFO -   优秀概率: 0.7700
2025-07-30 19:30:40,382 - optimization_system - INFO -   较差概率: 0.2300
2025-07-30 19:30:40,383 - optimization_system - INFO - ==================================================
2025-07-30 19:30:40,383 - optimization_system - INFO - 帕累托分类器训练完成！
2025-07-30 19:30:40,383 - optimization_system - INFO - ==================================================
2025-07-30 19:30:40,383 - optimization_system - INFO - 模型文件保存在: models
2025-07-30 19:30:40,384 - optimization_system - INFO - 可以使用以下文件:
2025-07-30 19:30:40,384 - optimization_system - INFO -   - pareto_sequence_classifier.joblib: 帕累托分类器
2025-07-30 19:30:40,384 - optimization_system - INFO -   - pareto_sequence_scaler.joblib: 特征标准化器
2025-07-30 19:30:40,384 - optimization_system - INFO -   - pareto_classification.joblib: 样本分类结果
2025-07-30 19:31:03,679 - optimization_system - INFO - 开始基于分类学习的NSGA-III优化
2025-07-30 19:31:03,680 - optimization_system - INFO - 初始化基于分类学习的NSGA-III优化器...
2025-07-30 19:31:03,767 - optimization_system - INFO - 跳过帕累托分类器训练，将直接加载已训练的模型
2025-07-30 19:31:03,769 - optimization_system - INFO - 加载训练好的分类器...
2025-07-30 19:31:04,581 - optimization_system - INFO - 开始执行基于分类学习的NSGA-III优化...
2025-07-30 20:08:46,518 - optimization_system - INFO - 开始基于分类学习的NSGA-III优化
2025-07-30 20:08:46,519 - optimization_system - INFO - 初始化基于分类学习的NSGA-III优化器...
2025-07-30 20:08:46,574 - optimization_system - INFO - 跳过帕累托分类器训练，将直接加载已训练的模型
2025-07-30 20:08:46,575 - optimization_system - INFO - 加载训练好的分类器...
2025-07-30 20:08:47,105 - optimization_system - INFO - 开始执行基于分类学习的NSGA-III优化...
2025-07-30 20:30:49,360 - optimization_system - INFO - 开始基于分类学习的NSGA-III优化
2025-07-30 20:30:49,361 - optimization_system - INFO - 初始化基于分类学习的NSGA-III优化器...
2025-07-30 20:30:49,460 - optimization_system - INFO - 跳过帕累托分类器训练，将直接加载已训练的模型
2025-07-30 20:30:49,461 - optimization_system - INFO - 加载训练好的分类器...
2025-07-30 20:30:52,410 - optimization_system - INFO - 开始执行基于分类学习的NSGA-III优化...
2025-07-30 20:32:44,843 - optimization_system - ERROR - 优化过程中发生错误: 'DecisionTreeClassifier' object has no attribute 'monotonic_cst'
2025-07-30 20:32:44,874 - optimization_system - ERROR - Traceback (most recent call last):
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\run_classification_nsga3.py", line 363, in main
    results = optimizer.optimize()
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\classification_based_nsga3.py", line 317, in optimize
    results = self.nsga3_optimizer.optimize()
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\nsga3_optimizer.py", line 973, in optimize
    self.population = self._environmental_selection(combined_population)
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\nsga3_optimizer.py", line 673, in _environmental_selection
    return self._classification_based_selection(population)
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\nsga3_optimizer.py", line 690, in _classification_based_selection
    fronts = self._non_dominated_sorting(population)
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\nsga3_optimizer.py", line 500, in _non_dominated_sorting
    return self._classification_based_sorting(population)
  File "D:\PycharmProjects\Chemical-Optimization_0728\pso_moead_optimization_model\src\nsga3_optimizer.py", line 526, in _classification_based_sorting
    prediction = self.pareto_classifier.predict(features_scaled)[0]
  File "D:\anaconda3\envs\chemo\lib\site-packages\sklearn\ensemble\_forest.py", line 904, in predict
    proba = self.predict_proba(X)
  File "D:\anaconda3\envs\chemo\lib\site-packages\sklearn\ensemble\_forest.py", line 946, in predict_proba
    X = self._validate_X_predict(X)
  File "D:\anaconda3\envs\chemo\lib\site-packages\sklearn\ensemble\_forest.py", line 633, in _validate_X_predict
    if self.estimators_[0]._support_missing_values(X):
  File "D:\anaconda3\envs\chemo\lib\site-packages\sklearn\tree\_classes.py", line 188, in _support_missing_values
    and self.monotonic_cst is None
AttributeError: 'DecisionTreeClassifier' object has no attribute 'monotonic_cst'

