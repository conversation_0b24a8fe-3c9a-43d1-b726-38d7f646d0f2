# MOEA/D多目标温度序列优化系统

基于分解的多目标进化算法(MOEA/D)的化工车间温度序列优化系统。同时优化统计偏差、模式匹配和阶段违反三个目标函数，以18个真实样本数据为起点进行数据驱动初始化。

## 🚀 快速开始

### 环境要求

- Python >= 3.8
- 主要依赖：numpy, pandas, scipy, scikit-learn, matplotlib, pyyaml

### 安装依赖

```bash
pip install -r requirements.txt
```

## 📊 使用方法

### 运行MOEA/D多目标优化

```bash
# 使用默认参数运行
python main.py

自定义参数运行
# python main.py --population-size 50 --max-generations 200 --neighbor-size 20
```


### 运行NSGA-III多目标优化

```bash
# 1. 训练帕累托分类器
python train_pareto_classifier.py --save-analysis

# 2. 执行基于分类学习的NSGA-III优化
python run_classification_nsga3.py --save-plots
```

### 主要参数

- `--population-size`: 种群大小（默认50）
- `--max-generations`: 最大代数（默认200）
- `--neighbor-size`: 邻域大小（默认20）
- `--verbose`: 详细输出模式
- `--visualize`: 生成结果可视化

## 📈 输出结果

运行完成后自动生成（保存在 `results/`目录）：

- **最佳解序列**: `best_solution_*.csv`
- **最佳解目标值**: `best_objectives_*.csv`
- **Pareto前沿**: `pareto_front_*.csv`
- **目标函数值**: `pareto_objectives_*.csv`
- **收敛历史**: `convergence_history_*.csv`
- **优化结果**: `moead_results_*.json`
- **结果可视化**: `moead_results_*.png`
- **总结报告**: `summary_report_*.txt`

## ✨ MOEA/D多目标优化特性

### 三个目标函数

- 🎯 **f1**: 统计偏差最小化
- 🎯 **f2**: 模式匹配差异最小化
- 🎯 **f3**: 阶段违反最小化

### 数据驱动初始化

- ✅ **18个真实样本**：基于真实样本数据进行种群初始化
- ✅ **约束处理**：应用温度序列约束确保解的可行性
- ✅ **平均长度**：使用18个样本的平均长度作为优化序列长度

### 多目标优化过程

- 🔄 **分解策略**：使用Tchebycheff分解方法
- 🔄 **邻域更新**：基于权重向量的邻域结构
- 📊 **Pareto前沿**：维护非支配解集合
- 🎯 **超体积指标**：评估收敛性和多样性

## 🗂️ 数据要求

系统使用以下18个样本文件（排除Sample_8、Sample_13、Sample_19）：

```
data/Esterification/
├── Sample_1.xlsx
├── Sample_2.xlsx
├── ...
├── Sample_21.xlsx  (排除8,13,19)
```

## 🎯 算法优势

- **多目标优化**：同时优化多个相互冲突的目标
- **Pareto最优**：提供多样化的最优解集合
- **数据驱动**：基于真实样本数据初始化
- **约束处理**：确保解的工程可行性
