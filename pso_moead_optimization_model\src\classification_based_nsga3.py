#!/usr/bin/env python3
"""
基于分类学习的NSGA-III多目标优化算法

实现三个基于分类学习的目标函数：
1. 最小化label_1预测值
2. 最大化label_2预测值（转换为最小化）
3. 最大化温度序列稳定性（转换为最小化）

使用训练好的帕累托分类器进行序列质量评估。
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any
import logging
import yaml
import joblib
import os
from datetime import datetime

try:
    from .nsga3_optimizer import NSGA3Optimizer
    from .sequence_stability_evaluator import SequenceStabilityEvaluator
    from .pareto_classifier_trainer import ParetoClassifierTrainer
    from .data_processor import DataProcessor
    from .feature_extractor import FeatureExtractor
except ImportError:
    from nsga3_optimizer import NSGA3Optimizer
    from sequence_stability_evaluator import SequenceStabilityEvaluator
    from pareto_classifier_trainer import ParetoClassifierTrainer
    from data_processor import DataProcessor
    from feature_extractor import FeatureExtractor

logger = logging.getLogger(__name__)


class ClassificationBasedNSGA3:
    """基于分类学习的NSGA-III优化器"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        """
        初始化基于分类学习的NSGA-III优化器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        # 初始化组件
        self.nsga3_optimizer = NSGA3Optimizer(config_path)
        self.stability_evaluator = SequenceStabilityEvaluator()
        self.data_processor = DataProcessor(config_path)

        # 设置NSGA-III优化器使用MOEAD的配置参数
        if 'moead' in self.config:
            moead_config = self.config['moead']
            if 'nsga3' not in self.config:
                self.config['nsga3'] = {}

            # 映射MOEAD参数到NSGA-III
            self.config['nsga3']['population_size'] = moead_config.get('population_size', 50)
            self.config['nsga3']['max_generations'] = moead_config.get('max_generations', 200)

            # 重新初始化NSGA-III优化器以使用更新的配置
            self.nsga3_optimizer = NSGA3Optimizer(config_path)
        
        # 分类器组件
        self.label1_classifier = None
        self.label2_classifier = None
        self.pareto_classifier = None
        self.feature_extractor = None
        self.label1_scaler = None
        self.label2_scaler = None
        self.pareto_scaler = None
        
        # 数据
        self.quality_labels = {}
        
    def train_pareto_classifier(self) -> Dict[str, Any]:
        """
        训练基于帕累托支配关系的分类器
        
        Returns:
            训练结果字典
        """
        logger.info("开始训练基于帕累托支配关系的分类器...")
        
        # 初始化帕累托分类器训练器
        pareto_trainer = ParetoClassifierTrainer(self.config_path)
        
        # 加载和分析数据
        analysis_results = pareto_trainer.load_and_analyze_data()
        
        # 训练分类器
        training_results = pareto_trainer.train_pareto_classifier()
        
        # 保存分类器
        pareto_trainer.save_classifier()
        
        logger.info("帕累托分类器训练完成")
        
        return {
            'analysis_results': analysis_results,
            'training_results': training_results
        }
    
    def load_trained_classifiers(self, models_dir: str = "models"):
        """
        加载训练好的分类器
        
        Args:
            models_dir: 模型目录
        """
        logger.info("加载训练好的分类器...")
        
        # 加载特征提取器
        feature_extractor_path = os.path.join(models_dir, "feature_extractor.joblib")
        if os.path.exists(feature_extractor_path):
            self.feature_extractor = joblib.load(feature_extractor_path)
            logger.info("特征提取器加载成功")
        else:
            raise FileNotFoundError(f"特征提取器文件不存在: {feature_extractor_path}")
        
        # 加载label1分类器
        label1_path = os.path.join(models_dir, "label1_classifier.joblib")
        if os.path.exists(label1_path):
            classifier_data = joblib.load(label1_path)
            if isinstance(classifier_data, dict):
                self.label1_classifier = classifier_data['model']
                self.label1_scaler = classifier_data['scaler']
            else:
                self.label1_classifier = classifier_data
            logger.info("Label1分类器加载成功")
        else:
            logger.warning(f"Label1分类器文件不存在: {label1_path}")
        
        # 加载label2分类器
        label2_path = os.path.join(models_dir, "label2_classifier.joblib")
        if os.path.exists(label2_path):
            classifier_data = joblib.load(label2_path)
            if isinstance(classifier_data, dict):
                self.label2_classifier = classifier_data['model']
                self.label2_scaler = classifier_data['scaler']
            else:
                self.label2_classifier = classifier_data
            logger.info("Label2分类器加载成功")
        else:
            logger.warning(f"Label2分类器文件不存在: {label2_path}")
        
        # 加载帕累托分类器 - 增强错误处理和版本兼容性检查
        pareto_classifier_path = os.path.join(models_dir, "pareto_sequence_classifier.joblib")
        pareto_scaler_path = os.path.join(models_dir, "pareto_sequence_scaler.joblib")

        if os.path.exists(pareto_classifier_path) and os.path.exists(pareto_scaler_path):
            try:
                # 尝试加载帕累托分类器
                self.pareto_classifier = joblib.load(pareto_classifier_path)
                self.pareto_scaler = joblib.load(pareto_scaler_path)

                # 验证模型兼容性
                self._validate_pareto_model_compatibility()

                logger.info("帕累托分类器加载成功")
                logger.info(f"分类器类型: {type(self.pareto_classifier).__name__}")

                # 将帕累托分类器设置到NSGA-III优化器中
                self.nsga3_optimizer.set_pareto_classifier(
                    self.pareto_classifier,
                    self.pareto_scaler,
                    self.feature_extractor
                )
            except Exception as e:
                logger.error(f"帕累托分类器加载失败: {e}")
                logger.warning("将使用传统的非支配排序")
                self.pareto_classifier = None
                self.pareto_scaler = None
        else:
            logger.warning("帕累托分类器文件不存在，将使用传统的非支配排序")
        
        # 加载质量标签数据（用于归一化）
        self.quality_labels = self.data_processor.load_quality_labels()

    def _validate_pareto_model_compatibility(self):
        """
        验证帕累托分类器与当前环境的兼容性
        """
        try:
            # 创建测试特征向量
            test_features = np.random.random((1, 101))  # 假设特征维度为101

            # 测试标准化器
            test_scaled = self.pareto_scaler.transform(test_features)

            # 测试分类器预测
            test_prediction = self.pareto_classifier.predict(test_scaled)
            test_probabilities = self.pareto_classifier.predict_proba(test_scaled)

            logger.info("帕累托分类器兼容性验证通过")

        except AttributeError as e:
            if 'monotonic_cst' in str(e):
                logger.error("检测到 scikit-learn 版本兼容性问题")
                logger.error("模型是用较新版本的 scikit-learn 训练的")
                logger.error("解决方案：升级 scikit-learn 到 1.4.0+ 版本或重新训练模型")
                raise RuntimeError("模型版本不兼容，需要重新训练") from e
            else:
                raise e
        except Exception as e:
            logger.error(f"帕累托分类器兼容性验证失败: {e}")
            raise e

    def create_classification_based_objectives(self):
        """
        创建基于分类学习的目标函数（简化版，主要用于记录和分析）

        注意：当使用帕累托分类器时，个体的适应度主要由分类器决定，
        而不是传统的目标函数值比较。

        Returns:
            多目标函数
        """
        def multi_objective_function(temperature_sequence: np.ndarray) -> List[float]:
            """
            基于分类学习的多目标函数（简化版）

            Args:
                temperature_sequence: 温度序列

            Returns:
                [f1, f2, f3] 目标函数值列表（主要用于记录和分析）
            """
            try:
                objectives = []

                # 目标1: label_1相关指标（用于记录）
                if self.label1_classifier is not None and self.feature_extractor is not None:
                    f1 = self._predict_label_value(temperature_sequence, 'label_1')
                else:
                    f1 = np.mean(temperature_sequence) / 100.0

                objectives.append(float(f1))

                # 目标2: label_2相关指标（用于记录）
                if self.label2_classifier is not None and self.feature_extractor is not None:
                    f2 = self._predict_label_value(temperature_sequence, 'label_2')
                else:
                    f2 = (np.max(temperature_sequence) - np.min(temperature_sequence)) / 100.0

                objectives.append(float(f2))

                # 目标3: 温度序列稳定性（用于记录）
                stability_score = self.stability_evaluator.calculate_stability_score(temperature_sequence)
                f3 = stability_score

                objectives.append(float(f3))

                return objectives

            except Exception as e:
                logger.error(f"目标函数计算失败: {e}")
                # 返回默认值
                return [1.0, 1.0, 0.5]

        return multi_objective_function
    
    def _predict_label_value(self, temperature_sequence: np.ndarray, label_name: str) -> float:
        """
        预测标签值
        
        Args:
            temperature_sequence: 温度序列
            label_name: 标签名称 ('label_1' 或 'label_2')
            
        Returns:
            预测的标签值
        """
        # 获取对应的分类器和标准化器
        if label_name == 'label_1':
            classifier = self.label1_classifier
            scaler = self.label1_scaler
        elif label_name == 'label_2':
            classifier = self.label2_classifier
            scaler = self.label2_scaler
        else:
            raise ValueError(f"不支持的标签名称: {label_name}")
        
        if classifier is None or self.feature_extractor is None:
            return 0.0
        
        # 提取特征
        features = self.feature_extractor.extract_sequence_features(temperature_sequence)
        
        # 标准化特征
        if scaler is not None:
            features_scaled = scaler.transform(features.reshape(1, -1))
        else:
            features_scaled = features.reshape(1, -1)
        
        # 预测类别
        predicted_class = classifier.predict(features_scaled)[0]
        
        # 将类别转换为连续值
        if label_name in self.quality_labels:
            label_values = self.quality_labels[label_name]
            min_val, max_val = label_values.min(), label_values.max()
            
            # 假设分类器使用了5个分箱，将类别映射回连续值
            n_bins = 5
            bin_width = (max_val - min_val) / n_bins
            predicted_value = min_val + (predicted_class + 0.5) * bin_width
            
            return predicted_value
        else:
            # 如果没有标签数据，直接返回类别值
            return float(predicted_class)
    
    def _predict_sequence_quality(self, temperature_sequence: np.ndarray) -> float:
        """
        预测序列质量（使用帕累托分类器或稳定性评估器）
        
        Args:
            temperature_sequence: 温度序列
            
        Returns:
            质量评分（0-1，越高越好）
        """
        if self.pareto_classifier is not None and self.pareto_scaler is not None:
            # 使用帕累托分类器
            features = self.feature_extractor.extract_sequence_features(temperature_sequence)
            features_scaled = self.pareto_scaler.transform(features.reshape(1, -1))
            
            # 预测概率
            probabilities = self.pareto_classifier.predict_proba(features_scaled)[0]
            quality_score = probabilities[1]  # 优秀类别的概率
            
            return quality_score
        else:
            # 使用稳定性评估器
            return self.stability_evaluator.calculate_stability_score(temperature_sequence)
    
    def optimize(self) -> Dict[str, Any]:
        """
        执行基于分类学习的NSGA-III优化
        
        Returns:
            优化结果字典
        """
        logger.info("开始基于分类学习的NSGA-III优化...")
        
        # 设置基于分类学习的目标函数
        classification_objectives = self.create_classification_based_objectives()
        self.nsga3_optimizer.set_objective_function(classification_objectives)
        
        # 执行优化
        results = self.nsga3_optimizer.optimize()
        
        # 添加分类学习相关信息
        results['algorithm'] = 'Classification-based NSGA-III with Pareto Classifier'
        results['selection_method'] = 'Pareto Classifier' if self.pareto_classifier is not None else 'Traditional Non-dominated Sorting'
        results['objective_descriptions'] = {
            'f1': 'Label_1预测值（记录用）',
            'f2': 'Label_2预测值（记录用）',
            'f3': '温度序列稳定性（记录用）'
        }
        results['fitness_evaluation'] = {
            'method': '基于帕累托分类器的适应度评估' if self.pareto_classifier is not None else '传统目标函数比较',
            'description': '使用训练好的帕累托分类器直接判断个体优劣，无需传统的目标函数值比较'
        }
        
        logger.info("基于分类学习的NSGA-III优化完成")
        return results
    
    def evaluate_solution_quality(self, temperature_sequence: np.ndarray) -> Dict[str, float]:
        """
        评估单个解的质量
        
        Args:
            temperature_sequence: 温度序列
            
        Returns:
            质量评估结果
        """
        objectives = self.create_classification_based_objectives()(temperature_sequence)
        
        # 计算额外的质量指标
        stability_metrics = self.stability_evaluator.get_stability_metrics(temperature_sequence)
        
        result = {
            'f1_label1_prediction': objectives[0],
            'f2_negative_label2_prediction': objectives[1],
            'f3_negative_stability': objectives[2],
            'actual_stability_score': -objectives[2],
            'temperature_range': np.max(temperature_sequence) - np.min(temperature_sequence),
            'mean_temperature': np.mean(temperature_sequence),
            'temperature_std': np.std(temperature_sequence)
        }
        
        # 添加稳定性指标
        result.update(stability_metrics)
        
        return result


def main():
    """测试基于分类学习的NSGA-III优化器"""
    optimizer = ClassificationBasedNSGA3()
    
    # 训练帕累托分类器
    training_results = optimizer.train_pareto_classifier()
    print("帕累托分类器训练结果:")
    print(f"  优秀样本数量: {training_results['analysis_results']['pareto_results']['excellent_count']}")
    print(f"  较差样本数量: {training_results['analysis_results']['pareto_results']['poor_count']}")
    print(f"  分类器准确率: {training_results['training_results']['cv_accuracy_mean']:.4f}")
    
    # 加载分类器
    optimizer.load_trained_classifiers()
    
    # 执行优化
    results = optimizer.optimize()
    
    print("\nNSGA-III优化结果:")
    print(f"  总时间: {results['total_time']:.2f}秒")
    print(f"  帕累托前沿大小: {results['pareto_front_size']}")
    print(f"  参考点数量: {len(results['reference_points'])}")


if __name__ == "__main__":
    main()
