#!/usr/bin/env python3
"""
分类器训练主脚本

该脚本负责：
1. 加载和预处理数据
2. 构建成对比较数据集
3. 提取特征
4. 训练SVM分类器
5. 评估模型性能
6. 保存训练好的模型
"""

import os
import sys
import argparse
import logging
import joblib
from datetime import datetime

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.data_processor import DataProcessor
from src.feature_extractor import FeatureExtractor
from src.sequence_classifier import SequenceClassifier
from src.utils import load_config, setup_logging, create_directories, print_system_info


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='训练温度序列比较分类器')
    
    parser.add_argument('--config', type=str, default='config/config.yaml',
                       help='配置文件路径')
    parser.add_argument('--data-dir', type=str, default=None,
                       help='数据目录路径（覆盖配置文件设置）')
    parser.add_argument('--output-dir', type=str, default='models',
                       help='模型输出目录')
    parser.add_argument('--save-plots', action='store_true',
                       help='保存训练结果图表')
    parser.add_argument('--augment-data', action='store_true',
                       help='启用数据增强')
    parser.add_argument('--label1-weight', type=float, default=None,
                       help='label_1的权重（越低越好）')

    # 新增的ResNet+GRU和多目标相关参数
    parser.add_argument('--use-resnet-gru', action='store_true',
                       help='使用ResNet+GRU特征提取器（默认使用LSTM）')
    parser.add_argument('--enable-pca', action='store_true',
                       help='启用PCA降维')
    parser.add_argument('--pca-components', type=int, default=50,
                       help='PCA降维维度（默认50）')
    parser.add_argument('--pareto-analysis', action='store_true',
                       help='进行帕累托支配关系分析')
    parser.add_argument('--label1-bins', type=int, default=5,
                       help='label_1分箱数量（默认5）')
    parser.add_argument('--label2-bins', type=int, default=5,
                       help='label_2分箱数量（默认5）')
    parser.add_argument('--train-classification-predictor', action='store_true',
                       help='训练基于分类学习的标签预测器')

    parser.add_argument('--verbose', action='store_true',
                       help='详细输出模式')
    
    return parser.parse_args()


def main():
    """主训练流程"""
    # 解析参数
    args = parse_arguments()

    # 打印系统信息
    print_system_info()

    # 初始化logger为None，以便在异常处理中检查
    logger = None

    try:
        # 加载配置
        config = load_config(args.config)

        # 设置日志
        logger = setup_logging(config)
        logger.info("开始训练温度序列比较分类器")
        
        # 创建输出目录
        create_directories([args.output_dir, 'results'])
        
        # 如果指定了数据目录，覆盖配置
        if args.data_dir:
            config['data']['data_dir'] = args.data_dir
        
        # 步骤1: 数据处理
        logger.info("=" * 50)
        logger.info("步骤1: 加载和处理数据")
        logger.info("=" * 50)
        
        data_processor = DataProcessor(args.config)

        # 设置权重参数（如果指定）
        if args.label1_weight is not None:
            # 使用命令行参数
            new_l1 = args.label1_weight

            # 验证和设置权重
            if new_l1 < 0:
                logger.error("权重必须为非负数")
                return False

            if new_l1 == 0:
                logger.error("权重必须大于0")
                return False

            data_processor.set_quality_weights(new_l1)
            logger.info(f"质量权重已设置: label_1={new_l1}")

        # 加载温度序列和标签
        temperature_sequences = data_processor.load_temperature_sequences()
        quality_labels = data_processor.load_quality_labels()

        # 创建成对比较数据集
        pairwise_data = data_processor.create_pairwise_dataset()
        
        # 数据增强（如果启用）
        if args.augment_data or config['classifier']['data_augmentation']['enable']:
            logger.info("启用数据增强...")
            augmentation_config = config['classifier']['data_augmentation']
            pairwise_data = data_processor.augment_pairwise_data(
                augmentation_factor=augmentation_config['augmentation_factor'],
                noise_level=augmentation_config['noise_level']
            )
        
        # 打印数据统计
        stats = data_processor.get_dataset_statistics()
        logger.info("数据集统计信息:")
        for key, value in stats.items():
            logger.info(f"  {key}: {value}")
        
        # 步骤2: 特征提取
        logger.info("=" * 50)
        logger.info("步骤2: 特征提取")
        logger.info("=" * 50)

        # 根据命令行参数更新配置
        if args.use_resnet_gru:
            config['feature_extraction']['extractor_type'] = 'resnet_gru'
            logger.info("使用ResNet+GRU特征提取器")

        if args.enable_pca:
            config['feature_extraction']['pca']['enable'] = True
            config['feature_extraction']['pca']['n_components'] = args.pca_components
            logger.info(f"启用PCA降维，降维到{args.pca_components}维")

        feature_extractor = FeatureExtractor(args.config)

        # 提取特征
        X, feature_names = feature_extractor.fit_transform(pairwise_data)
        y = [pair['label'] for pair in pairwise_data]
        
        logger.info(f"特征提取完成:")
        logger.info(f"  特征矩阵形状: {X.shape}")
        logger.info(f"  特征数量: {len(feature_names)}")
        logger.info(f"  样本数量: {len(y)}")
        logger.info(f"  标签分布: 类别0={y.count(0)}, 类别1={y.count(1)}")
        
        # 步骤3: 训练分类器
        logger.info("=" * 50)
        logger.info("步骤3: 训练分类器")
        logger.info("=" * 50)
        
        classifier = SequenceClassifier(args.config)
        
        # 训练模型
        training_results = classifier.train(X, y, feature_names)
        
        # 打印训练结果
        logger.info("训练结果:")
        logger.info(f"  训练准确率: {training_results['train_accuracy']:.4f}")
        logger.info(f"  测试准确率: {training_results['test_accuracy']:.4f}")
        logger.info(f"  交叉验证准确率: {training_results['cv_accuracy_mean']:.4f} ± {training_results['cv_accuracy_std']:.4f}")
        logger.info(f"  测试AUC: {training_results['test_auc']:.4f}")

        # 步骤3.5: 训练分类预测器（如果启用）
        classification_predictor = None
        pareto_results = None

        if args.train_classification_predictor or args.pareto_analysis:
            logger.info("=" * 50)
            logger.info("步骤3.5: 训练分类预测器和帕累托分析")
            logger.info("=" * 50)

            try:
                from src.classification_based_nsga3 import ClassificationBasedNSGA3
                # 使用NSGA3版本的ClassificationBasedPredictor
                ClassificationBasedPredictor = ClassificationBasedNSGA3

                # 训练分类预测器
                if args.train_classification_predictor:
                    logger.info("训练基于分类学习的标签预测器...")

                    # 更新配置
                    if 'classification_predictor' not in config:
                        config['classification_predictor'] = {}
                    config['classification_predictor']['label1_bins'] = args.label1_bins
                    config['classification_predictor']['label2_bins'] = args.label2_bins

                    classification_predictor = ClassificationBasedPredictor(args.config)

                    # 训练帕累托分类器（NSGA3版本）
                    predictor_results = classification_predictor.train_pareto_classifier()

                    logger.info("帕累托分类器训练结果:")
                    if 'training_results' in predictor_results:
                        training_res = predictor_results['training_results']
                        logger.info(f"  分类器准确率: {training_res['cv_accuracy_mean']:.4f} ± {training_res['cv_accuracy_std']:.4f}")
                        logger.info(f"  特征数量: {training_res['feature_count']}")
                        logger.info(f"  训练样本数量: {training_res['sample_count']}")

                    if 'analysis_results' in predictor_results:
                        analysis_res = predictor_results['analysis_results']
                        pareto_res = analysis_res['pareto_results']
                        logger.info(f"  优秀样本数量: {pareto_res['excellent_count']}")
                        logger.info(f"  较差样本数量: {pareto_res['poor_count']}")

                # 帕累托分析（已集成到帕累托分类器训练中）
                if args.pareto_analysis:
                    logger.info("帕累托分析已集成到帕累托分类器训练中")
                    pareto_results = predictor_results if classification_predictor is not None else None

            except ImportError as e:
                logger.warning(f"无法导入多目标函数模块: {e}")
            except Exception as e:
                logger.error(f"分类预测器训练或帕累托分析失败: {e}")

        # 步骤4: 保存模型
        logger.info("=" * 50)
        logger.info("步骤4: 保存模型")
        logger.info("=" * 50)
        
        # 保存分类器
        classifier.save_model(args.output_dir)
        
        # 保存特征提取器
        feature_extractor_path = os.path.join(args.output_dir,
                                            f"{config['model']['feature_extractor_name']}.joblib")
        joblib.dump(feature_extractor, feature_extractor_path)
        logger.info(f"特征提取器已保存到 {feature_extractor_path}")

        # 保存分类预测器（如果训练了）
        if classification_predictor is not None:
            predictor_path = os.path.join(args.output_dir, "classification_predictor.joblib")
            joblib.dump(classification_predictor, predictor_path)
            logger.info(f"分类预测器已保存到 {predictor_path}")

            # 保存帕累托分类器（NSGA3版本已自动保存）
            if classification_predictor is not None:
                logger.info("帕累托分类器已自动保存到models目录")
                logger.info("  - pareto_sequence_classifier.joblib: 帕累托分类器")
                logger.info("  - pareto_sequence_scaler.joblib: 特征标准化器")
                logger.info("  - pareto_classification.joblib: 样本分类结果")

        # 保存帕累托分析结果（如果进行了）
        if pareto_results is not None:
            import json
            pareto_path = os.path.join(args.output_dir, "pareto_analysis_results.json")

            # 转换numpy数组为列表以便JSON序列化
            serializable_results = {}
            for key, value in pareto_results.items():
                if key == 'objective_values':
                    # 转换目标函数值字典
                    serializable_results[key] = {str(k): list(v) for k, v in value.items()}
                else:
                    serializable_results[key] = value

            with open(pareto_path, 'w', encoding='utf-8') as f:
                json.dump(serializable_results, f, indent=2, ensure_ascii=False)
            logger.info(f"帕累托分析结果已保存到 {pareto_path}")
        
        # 保存训练摘要
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        summary_path = os.path.join(args.output_dir, f"training_summary_{timestamp}.txt")
        
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write("温度序列比较分类器训练摘要\n")
            f.write("=" * 50 + "\n")
            f.write(f"训练时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"配置文件: {args.config}\n")
            f.write(f"数据目录: {config['data']['data_dir']}\n")
            f.write("\n数据统计:\n")
            for key, value in stats.items():
                f.write(f"  {key}: {value}\n")
            f.write(f"\n特征信息:\n")
            f.write(f"  特征数量: {len(feature_names)}\n")
            f.write(f"  样本数量: {len(y)}\n")
            f.write(f"  标签分布: 类别0={y.count(0)}, 类别1={y.count(1)}\n")
            f.write(f"\n训练结果:\n")
            for key, value in training_results.items():
                if key not in ['train_classification_report', 'test_classification_report', 'confusion_matrix']:
                    f.write(f"  {key}: {value}\n")

            # 添加新的训练信息
            f.write(f"\n算法配置:\n")
            f.write(f"  特征提取器类型: {'ResNet+GRU' if args.use_resnet_gru else 'LSTM'}\n")
            f.write(f"  PCA降维: {'启用' if args.enable_pca else '禁用'}\n")
            if args.enable_pca:
                f.write(f"  PCA维度: {args.pca_components}\n")
            f.write(f"  分类预测器训练: {'启用' if args.train_classification_predictor else '禁用'}\n")
            f.write(f"  帕累托分析: {'启用' if args.pareto_analysis else '禁用'}\n")

            if classification_predictor is not None:
                f.write(f"\n分类预测器结果:\n")
                f.write(f"  Label1分箱数量: {args.label1_bins}\n")
                f.write(f"  Label2分箱数量: {args.label2_bins}\n")

            if pareto_results is not None:
                f.write(f"\n帕累托分析结果:\n")
                good_count = len([v for v in pareto_results['classification'].values() if v == 'good'])
                poor_count = len([v for v in pareto_results['classification'].values() if v == 'poor'])
                f.write(f"  好样本数量: {good_count}\n")
                f.write(f"  差样本数量: {poor_count}\n")
                f.write(f"  生成训练样本对: {pareto_results['training_data_size']}\n")
        
        logger.info(f"训练摘要已保存到 {summary_path}")
        
        # 步骤5: 生成图表（如果启用）
        if args.save_plots:
            logger.info("=" * 50)
            logger.info("步骤5: 生成训练结果图表")
            logger.info("=" * 50)
            
            plot_path = os.path.join('results', f"training_results_{timestamp}.png")
            classifier.plot_training_results(plot_path)
        
        # 步骤6: 模型验证
        logger.info("=" * 50)
        logger.info("步骤6: 模型验证")
        logger.info("=" * 50)
        
        # 使用部分数据进行验证
        validation_results = classifier.evaluate_model(X, y)
        logger.info("验证结果:")
        logger.info(f"  准确率: {validation_results['accuracy']:.4f}")
        logger.info(f"  AUC: {validation_results['auc']:.4f}")
        
        # 特征重要性分析
        feature_importance = classifier.get_feature_importance()
        if feature_importance:
            logger.info("特征重要性 (Top 10):")
            sorted_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)
            for i, (feature, importance) in enumerate(sorted_features[:10]):
                logger.info(f"  {i+1}. {feature}: {importance:.4f}")
        
        logger.info("=" * 50)
        logger.info("分类器训练完成！")
        logger.info("=" * 50)
        logger.info(f"模型文件保存在: {args.output_dir}")
        logger.info(f"训练摘要保存在: {summary_path}")
        if args.save_plots:
            logger.info(f"训练图表保存在: {plot_path}")
        
        return True
        
    except Exception as e:
        # 检查logger是否已初始化
        if logger is not None:
            logger.error(f"训练过程中发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())
        else:
            # 如果logger未初始化，使用print输出错误
            print(f"❌ 训练过程中发生错误: {e}")
            import traceback
            print("详细错误信息:")
            traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
