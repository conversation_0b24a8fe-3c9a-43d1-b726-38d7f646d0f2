# 基于分类学习的MOEA/D多目标优化系统

## 概述

本系统实现了基于分类学习的MOEA/D（Multi-Objective Evolutionary Algorithm based on Decomposition）多目标优化算法，用于化工车间温度序列的智能优化。

## 核心特性

### 三个基于分类学习的目标函数

1. **F1: 成对个体label_1的优劣比较**（最小化问题）
   - 使用训练好的label1分类器预测温度序列的label_1类别
   - 目标：最小化label_1预测值（越小越好）

2. **F2: 成对个体label_2标签的优劣比较**（最大化问题，转换为最小化）
   - 使用训练好的label2分类器预测温度序列的label_2类别
   - 目标：最大化label_2预测值（转换为最小化：-label_2）

3. **F3: 温度序列个体的优劣比较**（最大化问题，转换为最小化）
   - 使用训练好的序列比较分类器评估温度序列质量
   - 目标：最大化序列质量评分（转换为最小化：-质量评分）

### 算法架构

- **MOEA/D框架**：基于分解的多目标进化算法
- **分类学习集成**：利用预训练的机器学习分类器进行目标函数评估
- **Pareto前沿优化**：同时优化三个目标函数，生成Pareto最优解集

## 文件结构

```
pso_moead_optimization_model/
├── src/
│   ├── classification_based_moead.py    # 基于分类学习的MOEA/D实现
│   ├── data_processor.py                # 数据处理（支持label_1和label_2）
│   ├── sequence_classifier.py           # 序列比较分类器
│   ├── feature_extractor.py             # 特征提取器
│   ├── moead_optimizer.py               # MOEA/D优化器
│   └── utils.py                         # 工具函数
├── config/
│   └── config.yaml                      # 配置文件（已更新支持label_2）
├── train_classifier.py                 # 分类器训练脚本（已更新）
├── run_classification_moead.py          # 基于分类学习的优化主程序
└── README_classification_moead.md       # 本文档
```

## 使用流程

### 1. 数据准备

确保data目录包含以下文件：
```
data/Esterification/
├── Sample_1.xlsx ~ Sample_21.xlsx      # 温度序列数据
├── label_1.xlsx                        # 标签1数据
└── label_2.xlsx                        # 标签2数据
```

### 2. 训练分类器

```bash
# 训练所有分类器（包括label1、label2和序列比较分类器）
python train_classifier.py --train-classification-predictor --label1-bins 5 --label2-bins 5

# 可选参数：
# --config: 配置文件路径
# --data-dir: 数据目录路径
# --output-dir: 模型输出目录
# --label1-bins: label_1分箱数量
# --label2-bins: label_2分箱数量
```

训练完成后，models目录将包含：
- `feature_extractor.joblib`: 特征提取器
- `sequence_classifier.joblib`: 序列比较分类器
- `label1_classifier.joblib`: Label1分类器
- `label2_classifier.joblib`: Label2分类器

### 3. 执行基于分类学习的MOEA/D优化

```bash
# 运行基于分类学习的多目标优化
python run_classification_moead.py --save-plots

# 可选参数：
# --config: 配置文件路径
# --models-dir: 训练好的模型目录
# --output-dir: 结果输出目录
# --generations: 优化代数
# --population-size: 种群大小
# --save-plots: 保存结果图表
```

### 4. 结果分析

优化完成后，results目录将包含：
- `classification_moead_results_YYYYMMDD_HHMMSS.json`: 完整优化结果
- `classification_pareto_front_YYYYMMDD_HHMMSS.csv`: Pareto前沿数据
- `classification_best_solutions_YYYYMMDD_HHMMSS.csv`: 最优解集
- `classification_moead_results_YYYYMMDD_HHMMSS.png`: 结果可视化图表

## 配置说明

### 关键配置参数

```yaml
# 数据配置
data:
  label_files:
    label_1: "label_1.xlsx"
    label_2: "label_2.xlsx"  # 新增支持

# MOEA/D配置
moead:
  population_size: 18        # 种群大小
  max_generations: 100       # 最大代数
  neighbor_size: 10          # 邻域大小
  F: 0.5                     # 差分进化缩放因子
  CR: 0.9                    # 交叉概率

# 分类预测器配置
classification_predictor:
  label1_bins: 5             # label_1分箱数量
  label2_bins: 5             # label_2分箱数量
```

## 算法优势

1. **数据驱动**：基于真实工业数据训练的分类器进行目标函数评估
2. **多目标优化**：同时考虑多个工艺指标的优化
3. **智能决策**：利用机器学习模型的预测能力指导优化过程
4. **Pareto最优**：生成多个非支配解，为决策者提供选择空间
5. **可扩展性**：易于添加新的分类器和目标函数

## 技术特点

- **分类学习集成**：将机器学习分类器无缝集成到进化算法中
- **目标函数转换**：自动处理最大化和最小化问题的转换
- **鲁棒性设计**：包含异常处理和默认值机制
- **结果可视化**：提供丰富的图表展示优化过程和结果

## 注意事项

1. **模型依赖**：需要先训练好相应的分类器模型
2. **数据质量**：优化效果依赖于训练数据的质量和代表性
3. **参数调优**：可能需要根据具体问题调整MOEA/D参数
4. **计算资源**：大规模优化可能需要较长的计算时间

## 扩展建议

1. **新增目标函数**：可以训练更多分类器来增加新的优化目标
2. **集成学习**：使用集成学习方法提高分类器的预测精度
3. **在线学习**：实现在线更新分类器以适应新的数据
4. **多模态优化**：扩展算法以处理多模态优化问题
