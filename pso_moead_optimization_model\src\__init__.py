#!/usr/bin/env python3
"""
基于分类学习的NSGA-III多目标温度序列优化系统

基于NSGA-III算法的温度序列优化系统，集成分类学习方法。
同时优化label_1预测、label_2预测和温度序列稳定性三个目标函数。

主要模块：
- nsga3_optimizer: NSGA-III多目标优化算法实现
- classification_based_nsga3: 基于分类学习的NSGA-III集成
- business_data_analyzer: 业务数据分析器
- sequence_stability_evaluator: 温度序列稳定性评估器
- pareto_classifier_trainer: 帕累托分类器训练器
- sequence_classifier: 序列分类器
- feature_extractor: 特征提取器
- utils: 工具函数

作者: AI Assistant
版本: 5.0.0 (NSGA-III专用版本)
"""

__version__ = "5.0.0"
__author__ = "AI Assistant"
__email__ = ""
__description__ = "基于分类学习的NSGA-III多目标温度序列优化系统"

# 导入主要类和函数
from .nsga3_optimizer import NSGA3Optimizer
from .classification_based_nsga3 import ClassificationBasedNSGA3
from .business_data_analyzer import BusinessDataAnalyzer
from .sequence_stability_evaluator import SequenceStabilityEvaluator
from .pareto_classifier_trainer import ParetoClassifierTrainer
from .sequence_classifier import SequenceClassifier
from .feature_extractor import FeatureExtractor
from .utils import load_config, setup_logging, create_directories

__all__ = [
    "NSGA3Optimizer",
    "ClassificationBasedNSGA3",
    "BusinessDataAnalyzer",
    "SequenceStabilityEvaluator",
    "ParetoClassifierTrainer",
    "SequenceClassifier",
    "FeatureExtractor",
    "load_config",
    "setup_logging",
    "create_directories"
]
