#!/usr/bin/env python3
"""
帕累托分类器训练主脚本

该脚本负责：
1. 加载温度序列数据、label_1和label_2数据
2. 计算温度序列稳定性指标
3. 基于帕累托支配关系将样本分为优秀和较差两类
4. 训练基于帕累托分类的温度序列分类器
5. 保存训练好的分类器
"""

import os
import sys
import argparse
import logging
import joblib
from datetime import datetime
import numpy as np
import pandas as pd

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.pareto_classifier_trainer import ParetoClassifierTrainer
from src.utils import load_config, setup_logging, create_directories, print_system_info


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='训练基于帕累托支配关系的温度序列分类器')
    
    parser.add_argument('--config', type=str, default='config/config.yaml',
                       help='配置文件路径')
    parser.add_argument('--data-dir', type=str, default=None,
                       help='数据目录路径（覆盖配置文件设置）')
    parser.add_argument('--output-dir', type=str, default='models',
                       help='模型输出目录')
    parser.add_argument('--save-analysis', action='store_true',
                       help='保存帕累托分析结果')
    parser.add_argument('--verbose', action='store_true',
                       help='详细输出模式')
    
    return parser.parse_args()


def save_analysis_results(analysis_results: dict, training_results: dict,
                         output_dir: str, timestamp: str, logger=None):
    """
    保存分析结果

    Args:
        analysis_results: 数据分析结果
        training_results: 训练结果
        output_dir: 输出目录
        timestamp: 时间戳
        logger: 日志记录器
    """
    # 保存帕累托分析结果
    pareto_results = analysis_results['pareto_results']
    
    # 创建样本分类DataFrame
    sample_classification = []
    for sample_id in range(1, analysis_results['sample_count'] + 1):
        if sample_id in pareto_results['excellent_samples']:
            classification = 'excellent'
        elif sample_id in pareto_results['poor_samples']:
            classification = 'poor'
        else:
            classification = 'unknown'
        
        sample_classification.append({
            'sample_id': sample_id,
            'classification': classification
        })
    
    classification_df = pd.DataFrame(sample_classification)
    classification_path = os.path.join(output_dir, f"pareto_sample_classification_{timestamp}.csv")
    classification_df.to_csv(classification_path, index=False)

    if logger:
        logger.info(f"样本分类结果已保存到 {classification_path}")
    else:
        print(f"✅ 样本分类结果已保存到 {classification_path}")

    # 保存目标函数矩阵
    objectives_df = pd.DataFrame(
        analysis_results['objectives_matrix'],
        columns=['label_1', 'negative_label_2', 'negative_stability']
    )
    objectives_df['sample_id'] = range(1, len(objectives_df) + 1)
    objectives_path = os.path.join(output_dir, f"objectives_matrix_{timestamp}.csv")
    objectives_df.to_csv(objectives_path, index=False)

    if logger:
        logger.info(f"目标函数矩阵已保存到 {objectives_path}")
    else:
        print(f"✅ 目标函数矩阵已保存到 {objectives_path}")
    
    # 保存训练摘要
    summary_path = os.path.join(output_dir, f"pareto_training_summary_{timestamp}.txt")
    
    with open(summary_path, 'w', encoding='utf-8') as f:
        f.write("基于帕累托支配关系的温度序列分类器训练摘要\n")
        f.write("=" * 60 + "\n")
        f.write(f"训练时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"数据样本数量: {analysis_results['sample_count']}\n\n")
        
        f.write("帕累托分析结果:\n")
        f.write(f"  优秀样本: {pareto_results['excellent_samples']}\n")
        f.write(f"  优秀样本数量: {pareto_results['excellent_count']}\n")
        f.write(f"  较差样本: {pareto_results['poor_samples']}\n")
        f.write(f"  较差样本数量: {pareto_results['poor_count']}\n")
        f.write(f"  分类方法: {pareto_results.get('split_method', '综合排序')}\n\n")
        
        f.write("分类器训练结果:\n")
        for key, value in training_results.items():
            f.write(f"  {key}: {value}\n")
        
        f.write("\n目标函数说明:\n")
        f.write("  目标1 (label_1): 最小化，越小越好\n")
        f.write("  目标2 (-label_2): 最大化label_2转换为最小化，越小越好\n")
        f.write("  目标3 (-稳定性): 最大化稳定性转换为最小化，越小越好\n")
    
    if logger:
        logger.info(f"训练摘要已保存到 {summary_path}")
    else:
        print(f"✅ 训练摘要已保存到 {summary_path}")


def main():
    """主函数"""
    # 解析参数
    args = parse_arguments()
    
    # 打印系统信息
    print_system_info()
    
    # 初始化logger为None，以便在异常处理中检查
    logger = None
    
    try:
        # 加载配置
        config = load_config(args.config)
        
        # 设置日志
        logger = setup_logging(config)
        logger.info("开始训练基于帕累托支配关系的温度序列分类器")
        
        # 创建输出目录
        create_directories([args.output_dir])
        
        # 如果指定了数据目录，覆盖配置
        if args.data_dir:
            config['data']['data_dir'] = args.data_dir
            logger.info(f"数据目录设置为: {args.data_dir}")
        
        # 步骤1: 初始化帕累托分类器训练器
        logger.info("=" * 50)
        logger.info("步骤1: 初始化帕累托分类器训练器")
        logger.info("=" * 50)
        
        trainer = ParetoClassifierTrainer(args.config)
        
        # 步骤2: 加载和分析数据
        logger.info("=" * 50)
        logger.info("步骤2: 加载和分析数据")
        logger.info("=" * 50)
        
        analysis_results = trainer.load_and_analyze_data()
        
        logger.info("数据分析完成:")
        logger.info(f"  样本数量: {analysis_results['sample_count']}")
        logger.info(f"  优秀样本数量: {analysis_results['pareto_results']['excellent_count']}")
        logger.info(f"  较差样本数量: {analysis_results['pareto_results']['poor_count']}")
        logger.info(f"  分类方法: {analysis_results['pareto_results']['split_method']}")
        
        # 步骤3: 训练帕累托分类器
        logger.info("=" * 50)
        logger.info("步骤3: 训练帕累托分类器")
        logger.info("=" * 50)
        
        training_results = trainer.train_pareto_classifier()
        
        logger.info("分类器训练完成:")
        logger.info(f"  交叉验证准确率: {training_results['cv_accuracy_mean']:.4f} ± {training_results['cv_accuracy_std']:.4f}")
        logger.info(f"  特征数量: {training_results['feature_count']}")
        logger.info(f"  训练样本数量: {training_results['sample_count']}")
        
        # 步骤4: 保存分类器
        logger.info("=" * 50)
        logger.info("步骤4: 保存分类器")
        logger.info("=" * 50)
        
        trainer.save_classifier(args.output_dir)
        
        # 步骤5: 保存分析结果（如果启用）
        if args.save_analysis:
            logger.info("=" * 50)
            logger.info("步骤5: 保存分析结果")
            logger.info("=" * 50)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_analysis_results(analysis_results, training_results, args.output_dir, timestamp, logger)
        
        # 步骤6: 验证分类器
        logger.info("=" * 50)
        logger.info("步骤6: 验证分类器")
        logger.info("=" * 50)
        
        # 使用第一个样本进行测试
        if trainer.temperature_sequences:
            test_sample_id = list(trainer.temperature_sequences.keys())[0]
            test_sequence = trainer.temperature_sequences[test_sample_id]
            
            prediction_result = trainer.predict_sequence_quality(test_sequence)
            
            logger.info(f"测试样本 {test_sample_id} 的预测结果:")
            logger.info(f"  预测类别: {prediction_result['interpretation']}")
            logger.info(f"  置信度: {prediction_result['confidence']:.4f}")
            logger.info(f"  优秀概率: {prediction_result['probability_excellent']:.4f}")
            logger.info(f"  较差概率: {prediction_result['probability_poor']:.4f}")
        
        logger.info("=" * 50)
        logger.info("帕累托分类器训练完成！")
        logger.info("=" * 50)
        logger.info(f"模型文件保存在: {args.output_dir}")
        logger.info("可以使用以下文件:")
        logger.info("  - pareto_sequence_classifier.joblib: 帕累托分类器")
        logger.info("  - pareto_sequence_scaler.joblib: 特征标准化器")
        logger.info("  - pareto_classification.joblib: 样本分类结果")
        
        return True
        
    except Exception as e:
        # 检查logger是否已初始化
        if logger is not None:
            logger.error(f"训练过程中发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())
        else:
            # 如果logger未初始化，使用print输出错误
            print(f"❌ 训练过程中发生错误: {e}")
            import traceback
            print("详细错误信息:")
            traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
